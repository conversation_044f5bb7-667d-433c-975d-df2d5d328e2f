<div class="container">
    <!-- Header Section -->
    <div class="row">
      <div class="col-4">
        <p class="page-title">Update Appointment</p>
      </div>
      <div class="col-5"></div>
      <div class="col-3">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ Update Appointment
        </p>
      </div>
    </div>
  
    <!-- Main Content -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card border-0 custom-shadow shadow-lg">
          <form class="p-4">
            <!-- Search Section -->
            <div class="section-container mb-4">
              <h3 class="text-custom-dark mb-3">Search Appointment</h3>
              <div class="row g-3">
                <div class="col-md-4 d-flex">
                  <input type="text" class="form-control bg-light me-2" placeholder="Search" [(ngModel)]="searchText" name="search">
                  <button class="btn btn-success" (click)="searchPatient()">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
              </div>
            </div>
  
            <!-- Patient Information -->
            <div class="section-container mb-4" *ngIf="isDataLaod">
              <h3 class="text-custom-dark mb-3">Patient Information</h3>
              <div class="row g-3">
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" placeholder="Enter Name" readonly [value]="patientDetails.firstName + ' ' + patientDetails.lastName">
                </div>
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" placeholder="NIC" readonly [value]="patientDetails.nic">
                </div>
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" placeholder="Mobile Number" readonly [value]="patientDetails.contactNo">
                </div>
                <div class="col-md-6">
                  <input type="email" class="form-control bg-light" placeholder="Email Address" readonly [value]="patientDetails.email">
                </div>
                <div class="col-md-4">
                  <!-- <div class="btn-group w-100">
                    <input type="checkbox" class="btn-check" [checked]="genderChecker()" disabled>
                    <label class="btn btn-outline-success">Gender</label>
                  </div> -->
                </div>
              </div>
            </div>
  
            <!-- Appointment Details -->
            <div class="section-container mb-4" *ngIf="isDataLaod">
              <h3 class="text-custom-dark mb-3">Appointment Details</h3>
              <div class="row g-3">
                <div class="col-md-6">
                  <input type="date" class="form-control bg-light" [(ngModel)]="appintmentDetails[0].date" name="date">
                </div>
                <div class="col-md-6">
                  <input type="time" class="form-control bg-light" [(ngModel)]="appintmentDetails[0].time" name="time">
                </div>
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" placeholder="Description" [(ngModel)]="appintmentDetails[0].description" name="description">
                </div>
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" placeholder="Category" [(ngModel)]="appintmentDetails[0].categoryId" name="category">
                </div>
              </div>
            </div>
  
            <!-- Submit Button -->
            <div class="d-flex justify-content-end mt-4" *ngIf="isDataLaod">
              <button type="button" class="btn btn-success px-4" (click)="update()">Update Appointment</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>