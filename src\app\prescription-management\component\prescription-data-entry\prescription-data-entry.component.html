<div class="alerts-container">
  <app-alert *ngFor="let alert of alerts" [alert]="alert"></app-alert>
</div>

<div class="card">
  <div class="card-body">
    <div class="row g-2 align-items-center">
      <div class="col">
        <input
          [(ngModel)]="drugData.drugName"
          type="text"
          class="form-control bg-light"
          id="drug-name"
          placeholder="Drug Name"
        />
      </div>
      <div class="col">
        <input
          [(ngModel)]="drugData.dosage"
          type="text"
          class="form-control bg-light"
          id="dosage"
          placeholder="Dosage"
        />
      </div>
      <div class="col">
        <input
          [(ngModel)]="drugData.frequency"
          type="text"
          class="form-control bg-light"
          id="frequency"
          placeholder="Frequency"
        />
      </div>
      <div class="col-auto">
        <button (click)="add()" id="add-drug" class="btn btn-success w-100">
          {{ editingIndex !== null ? "✔" : "+" }}
        </button>
      </div>
    </div>

    <br />
    <div
      class="modal-body"
      style="width: 100%; overflow: auto; max-height: 200px"
    >
      <table class="table table-hover table-striped">
        <thead>
          <tr class="text-center">
            <th scope="col" class="text-success">Count</th>
            <th scope="col" class="text-success">Drug</th>
            <th scope="col" class="text-success">Dosage</th>
            <th scope="col" class="text-success">Frequency</th>
            <th scope="col" class="text-success">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let prescriptionDetails of drugsList; let i = index"
            class="text-center"
          >
            <td>{{ i + 1 }}</td>
            <td>{{ prescriptionDetails.drugName }}</td>
            <td>{{ prescriptionDetails.dosage }}</td>
            <td>{{ prescriptionDetails.frequency }}</td>
            <td class="d-flex justify-content-center">
              <button class="btn p-0 me-2" (click)="edit(i)">
                <i class="bi bi-pencil-square text-success"></i>
              </button>
              <button class="btn p-0 me-2" (click)="delete(i)">
                <i class="bi bi-trash text-danger"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <br />
    <div class="d-flex justify-content-end">
      <button
        type="button"
        class="btn btn-success"
        data-bs-toggle="modal"
        data-bs-target="#myModal"
      >
        Review
      </button>
    </div>
  </div>
</div>

<!-- ---------------------------------- -->

<div class="container">
  <div class="row">
    <div>
      <!-- The Modal -->
      <div class="modal" id="myModal">
        <div class="modal-dialog modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
              <h4 class="modal-title" class="text-success">
                Proceed the prescription?
              </h4>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
              ></button>
            </div>

            <!-- Modal body -->
            <div
              class="modal-body"
              style="width: 100%; overflow: auto; max-height: 400px"
            >
              <table class="table table-hover table-striped">
                <thead>
                  <tr class="text-center">
                    <th scope="col" class="text-success">Count</th>
                    <th scope="col" class="text-success">Drug</th>
                    <th scope="col" class="text-success">Dosage</th>
                    <th scope="col" class="text-success">Frequency</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    class="text-center"
                    *ngFor="let prescriptionDetails of drugsList; let i = index"
                  >
                    <td>{{ i + 1 }}</td>
                    <td>{{ prescriptionDetails.drugName }}</td>
                    <td>{{ prescriptionDetails.dosage }}</td>
                    <td>{{ prescriptionDetails.frequency }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
              <button
                type="button"
                class="btn btn-success"
                data-bs-dismiss="modal"
                (click)="confirmSavePrescription()"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
