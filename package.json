{"name": "hms", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:HMS": "node dist/hms/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/platform-server": "^18.2.0", "@angular/router": "^18.2.0", "@angular/ssr": "^18.2.3", "@types/http-errors": "^2.0.4", "@types/http-proxy": "^1.17.15", "bootstrap": "5.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.7", "date-fns": "^4.1.0", "express": "^4.18.2", "hms": "file:", "html2canvas": "^1.4.1", "i": "^0.3.7", "icons": "^1.0.0", "jspdf": "^2.5.2", "npm": "^10.9.0", "rxjs": "~7.8.0", "sweetalert2": "^11.6.13", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@analogjs/vite-plugin-angular": "^1.9.1", "@angular-devkit/build-angular": "^18.2.3", "@angular/cli": "^18.2.11", "@angular/compiler-cli": "^18.2.0", "@types/express": "^4.17.21", "@types/jasmine": "~5.1.0", "@types/jquery": "^3.5.32", "@types/node": "^18.18.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}