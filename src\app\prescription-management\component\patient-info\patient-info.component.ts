import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Patient } from '../../model/patient.model';
import { SearchService } from './../../service/search.service';
import { HoldPrescription } from '../../model/hold-prescription.model';
import { HoldPrescriptionService } from '../../service/hold-prescription.service';
import { AlertComponent } from '../alert/alert.component';
import { Alert, AlertService } from '../../service/alert.service';
import { Appointment } from '../../model/appointment.model';
import { Medication } from '../../model/medication';
@Component({
  selector: 'app-patient-info',
  standalone: true,
  imports: [FormsModule, CommonModule, ReactiveFormsModule, AlertComponent],
  templateUrl: './patient-info.component.html',
  styleUrls: ['./patient-info.component.css'],
})
export class PatientInfoComponent implements OnInit {
  alerts: Alert[] = [];
  patient: Patient = {} as Patient;
  searchTerm: string = '';
  errorMessage: string = '';
  loading: boolean = false;
  holdPrescriptionData: HoldPrescription = {} as HoldPrescription;
  currentAppointment: Appointment[] = [];
  drugsList: Medication[] = [];

  constructor(
    private searchService: SearchService,
    private holdPrescriptionService: HoldPrescriptionService,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.alertService.alert$.subscribe((alerts) => {
      this.alerts = alerts;
    });

    this.searchService.patientData$.subscribe((patientData: any) => {
      this.patient = patientData;
    });

    this.searchService.appointmentData$.subscribe((appointmentData: any) => {
      this.currentAppointment = appointmentData;
    });
  }

  holdPatient(): void {
    const currentDrugList = localStorage.getItem('drugsList');
    this.drugsList = currentDrugList ? JSON.parse(currentDrugList) : null;

    console.log(this.drugsList);

    this.holdPrescriptionData = {
      patientId: this.patient.id,
      patientName: this.patient.firstName + ' ' + this.patient.lastName,
      patientEmail: this.patient.email,
      appointmentId:
        this.currentAppointment[this.currentAppointment.length - 1]
          .appointmentId,
      medications: this.drugsList,
    };

    this.holdPrescriptionService
      .holdPatientData(this.holdPrescriptionData)
      .subscribe(
        (response: string) => {
          this.alertService.showAlert(
            'success',
            'Patient data held successfully.'
          );
          console.log('Patient data held successfully:', response);
        },
        (error) => {
          if (error.status === 400) {
            this.alertService.showAlert(
              'error',
              'Please provide a medication to hold the patient.'
            );
          } else {
            this.alertService.showAlert('error', 'Unexpected error occurred.');
          }
          console.error('Error details:', error);
        }
      );
  }
}
