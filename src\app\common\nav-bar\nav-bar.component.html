<div class="sidebar" [class.collapsed]="isSidebarCollapsed">
  <div class="logo-container">
    <a routerLink="/" class="logo-link">
      <img src="logo.png" alt="WeCare Logo" class="logo-image">
      <span class="logo-text" [class.hide-text]="isSidebarCollapsed"><h2>WeCare</h2></span>
    </a>
  </div>
  
  <div class="menu-container">
    <ul class="nav-menu">
      <li class="menu-item" *ngFor="let item of menuItems">
        <a class="menu-link" (click)="item.submenu ? toggleMenu(item.id) : setSelectedItem(item.id)" 
           [class.active]="isItemSelected(item.id)" 
           [routerLink]="item.routerLink">
          <i [class]="item.icon"></i>
          <span [class.hide-text]="isSidebarCollapsed">{{ item.label }}</span>
          <i *ngIf="item.submenu" class="bi bi-chevron-down ms-auto" 
             [class.rotated]="isMenuOpen(item.id)"></i>
        </a>
        <ul *ngIf="item.submenu" class="submenu" [class.expanded]="isMenuOpen(item.id)">
          <li *ngFor="let subItem of item.submenu">
            <a [routerLink]="subItem.routerLink" 
               [class.active]="isItemSelected(subItem.id)" 
               (click)="setSelectedItem(subItem.id)">
              {{ subItem.label }}
            </a>
          </li>
        </ul>
      </li>
    </ul>

    <div class="bottom-menu">
      <a class="menu-link" routerLink="settings" 
         [class.active]="isItemSelected('settings')" 
         (click)="setSelectedItem('settings')">
        <i class="bi bi-gear-wide-connected"></i>
        <span [class.hide-text]="isSidebarCollapsed">Settings</span>
      </a>
      <a class="menu-link" (click)="setSelectedItem('sign-out')">
        <i class="bi bi-door-closed"></i>
        <span [class.hide-text]="isSidebarCollapsed">Sign out</span>
      </a>
    </div>
  </div>
</div>

