.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal {
    background-color: #e6ffe8;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: 320px;
    text-align: center;
  }
  
  .alert-box {
    width: 100%;
  }
  
  .alert-icon {
    font-size: 40px;
    color: green;
    margin-bottom: 10px;
  }
  
  .alert-content h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
  }
  
  .alert-details div {
    margin-bottom: 12px;
    text-align: left;
    margin-right: 16px;
  }
  
  .alert-details label {
    display: block;
    margin-bottom: 4px;
    font-size: 14px;
    color: #333;
  }
  
  .alert-details input {
    width: 100%;
    padding: 6px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    color: #333;
  }
  
  .alert-details input[readonly] {
    background-color: #e9e9e9;
  }
  
  .alert-button {
    margin-top: 20px;
  }
      
  .alert-button button {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin: 10px;
  }
  
  .alert-button button:hover {
    background-color: #45a049;
  }
  