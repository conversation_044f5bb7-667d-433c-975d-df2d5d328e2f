<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Delete Appointment</p>
    </div>
    <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ Delete Appointmnet
        </p>
      </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <div class="p-4">
          <!-- Search Section -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Search Appointment</h3>
            <div class="row g-3">
              <div class="col-md-6">
                <input type="search" class="form-control bg-light" [(ngModel)]="searchQuery" (input)="onSearchInput()" placeholder="Search by appointment ID">
              </div>
            </div>
          </div>

          <!-- Appointment Details -->
          <div class="section-container">
            <h3 class="text-custom-dark mb-3" *ngIf="showNoAppointmentMessage">
              No appointment found for "{{ searchQuery }}"
            </h3>
            <div class="row g-3" *ngIf="selectedAppointment.appointmentId && !showNoAppointmentMessage">
              <div class="col-12">
                <h3 class="text-custom-dark">Appointment ID: {{ selectedAppointment.appointmentId }}</h3>
              </div>
              <div class="col-md-6">
                <input type="text" class="form-control bg-light" placeholder="Enter Name" [(ngModel)]="selectedAppointment.name" readonly>
              </div>
              <div class="col-md-6">
                <input type="text" class="form-control bg-light" placeholder="NIC" [(ngModel)]="selectedAppointment.NIC" readonly>
              </div>
              <div class="col-md-6">
                <input type="text" class="form-control bg-light" placeholder="Mobile Number" [(ngModel)]="selectedAppointment.mobileNumber" readonly>
              </div>
              <div class="col-md-6">
                <input type="email" class="form-control bg-light" placeholder="Email Address" [(ngModel)]="selectedAppointment.emailAddress" readonly>
              </div>
              <div class="col-md-6">
                <input type="date" class="form-control bg-light" [(ngModel)]="selectedAppointment.date" readonly>
              </div>
              <div class="col-md-6">
                <input type="time" class="form-control bg-light" [(ngModel)]="selectedAppointment.time" readonly>
              </div>
              <div class="col-12">
                <textarea class="form-control bg-light" placeholder="Description" [(ngModel)]="selectedAppointment.description" readonly></textarea>
              </div>
              <!-- <div class="col-md-6">
                <input type="text" class="form-control bg-light" [(ngModel)]="selectedAppointment.category" readonly>
              </div> -->
              <div class="col-12 text-end mt-4">
                <button class="btn btn-danger px-4" (click)="onSubmit()">Delete Appointment</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Confirmation Modal -->
<div class="modal-overlay" *ngIf="showModal">
  <div class="modal-container">
    <div class="alert-box">
      <div class="alert-icon warning">
        <span>?</span>
      </div>
      <div class="alert-content">
        <h2>Do you want to delete this appointment?</h2>
        <div class="alert-details">
          <div class="detail-row">
            <label><strong>Appointment ID:</strong></label>
            <span>{{selectedAppointment.appointmentId}}</span>
          </div>
          <div class="detail-row">
            <label><strong>Name:</strong></label>
            <span>{{selectedAppointment.name}}</span>
          </div>
          <div class="detail-row">
            <label><strong>Date:</strong></label>
            <span>{{selectedAppointment.date}}</span>
          </div>
          <div class="detail-row">
            <label><strong>Time:</strong></label>
            <span>{{selectedAppointment.time}}</span>
          </div>
        </div>
      </div>
      <div class="alert-button">
        <button class="btn btn-secondary me-2" (click)="cancel()">Cancel</button>
        <button class="btn btn-danger" (click)="delete()">Delete</button>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal -->
<div class="modal-overlay" *ngIf="showModalVerify">
  <div class="modal-container">
    <div class="alert-box">
      <div class="alert-icon">
        <span>✓</span>
      </div>
      <div class="alert-content">
        <h2>Appointment Deleted Successfully</h2>
      </div>
      <div class="alert-button">
        <button class="btn btn-success" (click)="closeAll()">OK</button>
      </div>
    </div>
  </div>
</div>