<div class="container">
    <!-- Header Section -->
    <div class="row">
      <div class="col-4">
        <p class="page-title">View Categories</p>
      </div>
      <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ View Category
        </p>
      </div>
    </div>
  
    <!-- Main Content -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card border-0 custom-shadow shadow-lg">
          <div class="p-4">
            <!-- Search Section -->
            <div class="section-container mb-4">
              <div class="row g-3">
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" 
                         [(ngModel)]="searchText" placeholder="Search categories...">
                </div>
              </div>
            </div>
  
            <!-- Categories Table -->
            <div class="section-container">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Category ID</th>
                      <th>Description</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody *ngIf="!isEmpty(categoryList); else emptyCategory">
                    <tr *ngFor="let category of filteredData">
                      <td>{{category.categoryId}}</td>
                      <td>{{category.description}}</td>
                      <td>
                        <button class="btn btn-sm btn-outline-danger" (click)="deleteCategory(category)">
                          <i class="bi bi-trash-fill"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                  <ng-template #emptyCategory>
                    <tr>
                      <td colspan="3" class="text-center p-4">No categories found</td>
                    </tr>
                  </ng-template>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>