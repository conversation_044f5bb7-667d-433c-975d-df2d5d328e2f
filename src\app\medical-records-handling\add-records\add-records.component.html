<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Add Records</p>
    </div>
    <div class="col-6"></div>
    <div class="col-2">
      <p class="breadcrumb">
        <a routerLink="/" class="text-decoration-none">Home</a>/ Add Records
      </p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <div class="p-4">
          <div class="row">
            <!-- Left Column - Patient Report Details -->
            <div class="col-md-5">
              <div class="section-container h-100">
                <h3 class="text-custom-dark mb-3">Patient Report Details</h3>
                
                <!-- Date Picker -->
                <div class="mb-4">
                  <input type="date" id="reportDate" name="reportDate" 
                         class="form-control bg-light" autocomplete="off" />
                </div>

                <!-- Reports Section -->
                <div class="mb-4">
                  <h4 class="text-custom-dark mb-3">Available Reports</h4>
                  <div class="bg-light rounded p-3">
                    <div class="d-flex justify-content-between align-items-center mb-2"
                         *ngFor="let report of allReportList; let i = index" 
                         [id]="report.reportId">
                      <label class="form-check-label">{{ report.reportId }}</label>
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input" 
                               [(ngModel)]="allReportList[i].isChecked"
                               (change)="addReport(report, $event)"
                               [name]="'report' + i">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Prescriptions Section -->
                <div class="mb-4">
                  <h4 class="text-custom-dark mb-3">Available Prescriptions</h4>
                  <div class="bg-light rounded p-3">
                    <div class="d-flex justify-content-between align-items-center mb-2"
                         *ngFor="let prescription of displayPrescriptionList; let i = index" 
                         [id]="prescription.id">
                      <label class="form-check-label">{{ prescription.name }}</label>
                      <div class="form-check">
                        <input type="checkbox" class="form-check-input"
                               [(ngModel)]="displayPrescriptionList[i].isChecked"
                               (change)="addPrescription(prescription, $event)"
                               [name]="'prescription' + i">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column - Record Information -->
            <div class="col-md-7">
              <!-- Patient Search Section -->
              <div class="section-container mb-4">
                <h3 class="text-custom-dark mb-3">Patient Search</h3>
                <div class="row g-3">
                  <div class="col-md-8">
                    <input type="text" class="form-control bg-light" 
                           [(ngModel)]="patientIdToPayLoad" name="patientIdToPayLoad"
                           placeholder="Enter Patient ID" 
                           (keyup.enter)="searchPatientById(patientIdToPayLoad)">
                  </div>
                  <div class="col-md-4">
                    <button class="btn btn-success w-100" 
                            (click)="searchPatientById(patientIdToPayLoad)">Search</button>
                  </div>
                </div>
                
                <!-- Patient Info -->
                <div class="mt-3" *ngIf="searchedPatient">
                  <div class="bg-light rounded p-3">
                    <p class="mb-2"><strong>Name:</strong> {{ searchedPatient?.name }}</p>
                    <p class="mb-2"><strong>Gender:</strong> {{ searchedPatient?.gender }}</p>
                    <p class="mb-2"><strong>Age:</strong> {{ searchedPatient?.age }}</p>
                    <p class="mb-0"><strong>Contact:</strong> {{ searchedPatient?.contact }}</p>
                  </div>
                </div>
              </div>

              <!-- Selected Items Section -->
              <div class="section-container mb-4">
                <h3 class="text-custom-dark mb-3">Selected Items</h3>
                
                <!-- Selected Reports -->
                <div class="mb-3">
                  <label class="form-label">Selected Reports</label>
                  <div class="bg-light rounded p-3">
                    <div class="d-flex align-items-center justify-content-between mb-2" 
                         *ngFor="let report of addedReportList">
                      <span>{{report.reportId}}</span>
                      <i class="bi bi-x-circle text-danger" style="cursor: pointer" 
                         (click)="removeFromAddedReports(report)"></i>
                    </div>
                  </div>
                </div>

                <!-- Selected Prescriptions -->
                <div class="mb-3">
                  <label class="form-label">Selected Prescriptions</label>
                  <div class="bg-light rounded p-3">
                    <div class="d-flex align-items-center justify-content-between mb-2" 
                         *ngFor="let prescription of prescriptionList">
                      <span>{{prescription.name}}</span>
                      <i class="bi bi-x-circle text-danger" style="cursor: pointer" 
                         (click)="removeFromPrescriptions(prescription)"></i>
                    </div>
                  </div>
                </div>

                <!-- Notes -->
                <div class="mb-3">
                  <label class="form-label">Notes</label>
                  <textarea class="form-control bg-light" rows="3" 
                            [(ngModel)]="recordDescription" name="recordDescription"
                            placeholder="Additional notes..."></textarea>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-success px-4" (click)="saveRecord()">
                  Add Record
                </button>
                <button type="button" class="btn btn-secondary px-4" (click)="clearAllFields()">
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>