import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { RouterLink } from '@angular/router';

interface AppointmentForm {
  name: string;
  nic: string;
  mobile: string;
  email: string;
  gender: string;
  date: string;
  time: string;
  description: string;
  category: string;
}

@Component({
  selector: 'app-add-appointment',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './add-appointment.component.html',
  styleUrls: ['./add-appointment.component.css'],
})
export class AddAppointmentComponent implements OnInit {
  showModal = false;
  isLoading = false;

  appointmentId: string = '';
  description: string = '';
  date: string = '';
  time: string = '';
  patientId: string = '';
  categoryId: string = '';
  selectedGender: string = '';
  firstName: string = '';
  nic: string = '';
  contactNo: string = '';
  email: string = '';

  formData: AppointmentForm = {
    name: '',
    nic: '',
    mobile: '',
    email: '',
    gender: this.selectedGender,
    date: '2024-10-12',
    time: '10:00',
    description: '',
    category: '',
  };

  categoryList: any[] = []

  constructor(private http: HttpClient) {
    this.http.get(`${environment.CATEGORY_BASE_URL}/all`).subscribe({
      next: (response: any) => {
        for(let category of response){
          this.categoryList.push(category)
        }
      },
      error: (error) => {
        // alert('Some Went Wrong');
      },
    });
  }

  ngOnInit() {}

  selectGender(gender: string) {
    this.selectedGender = gender;
    this.formData.gender = gender;
  }

  isMaleSelected(): boolean {
    return this.selectedGender === 'male';
  }

  isFemaleSelected(): boolean {
    return this.selectedGender === 'female';
  }

  onSubmit() {
    if (
      !this.description ||
      !this.date ||
      !this.time ||
      !this.patientId ||
      !this.categoryId
    ) {
      alert('Please fill in all required fields');
      return;
    }

    this.isLoading = true;

    const appointment = {
      appointmentId: Number(this.appointmentId.slice(1)),
      description: this.description,
      date: this.date,
      time: this.time,
      patientId: this.patientId,
      categoryId: this.categoryId,
    };

    this.http
      .post(`${environment.APPOINTMENT_MANAGEMENT_BASE_URL}/add`, appointment)
      .subscribe({
        next: (response) => {
          this.formData.name = this.firstName;
          this.formData.mobile = this.contactNo;
          this.formData.category = this.categoryId;
          this.isLoading = false;
          this.onSubmits();
        },
        error: (error) => {
          this.isLoading = false;
          alert(`Error: ${error.error.message || 'Unable to add appointment'}`);
        },
      });
  }

  clearForm() {
    this.description = '';
    this.date = '';
    this.time = '';
    this.patientId = '';
    this.categoryId = '';
    this.firstName = '';
    this.nic = '';
    this.contactNo = '';
    this.email = '';
  }

  fetchPatientDetails() {
    if (!this.patientId.trim()) {
      alert('Please enter a valid Patient ID');
      return;
    }

    this.http
      .get(
        `http://localhost:8081/patient/patient-search-by-id/${this.patientId}`
      )
      .subscribe((res: any) => {
        if (res) {
          this.firstName = res.firstName || '';
          this.nic = res.nic || '';
          this.contactNo = res.contactNo || '';
          this.email = res.email || '';
        } else {
          alert('Patient not found');
        }
      });
  }

  public closeAlert() {
    this.showModal = false;
    this.clearForm();
  }

  public onSubmits() {
    this.showModal = true;
  }
}

