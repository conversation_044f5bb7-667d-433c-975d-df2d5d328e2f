<div class="alerts-container">
  <app-alert *ngFor="let alert of alerts" [alert]="alert"></app-alert>
</div>

<div class="container py-4">
  <!-- Patient Details Section -->
  <div class="card border-0 custom-shadow shadow-lg">
    <div class="row g-0">
      <div class="col-md-4 p-4 text-center rounded-start shadow-sm">
        <div
          *ngIf="patient; else defaultImage"
          class="profile-img mb-3 mx-auto"
          [ngClass]="{
            man:
              (patient && patient.gender === 'Male') ||
              (patient && patient.gender === 'Other'),
            women: patient && patient.gender === 'Female'
          }"
        ></div>

        <ng-template #defaultImage>
          <div class="profile-img mb-3 mx-auto man"></div>
        </ng-template>

        <h4 class="text-custom-dark mb-1">
          {{ patient && patient.firstName + " " + patient.lastName }}
        </h4>
        <p class="text-custom-green">{{ patient && patient.email }}</p>
        <div class="row g-3 mb-4">
          <div class="col-6">
            <h3 class="text-custom-dark mb-0" *ngIf="patient">
              {{
                 currentAppointment.length > 0 && currentAppointment[currentAppointment.length - 1]
                  .appointmentId
              }}
            </h3>
            <small class="text-custom-green">Appointments</small>
          </div>
          <div class="col-6">
            <h3 class="text-custom-dark mb-0">
              {{ patient && patient.bloodGroup }}
            </h3>
            <small class="text-custom-green">Blood</small>
          </div>
        </div>
        <button class="btn btn-outline-custom px-4 custom-button" (click)="holdPatient()">
          Hold Patient
        </button>
      </div>

      <div class="col-md-8 bg-light p-4 rounded-end shadow-sm">
        <div class="row g-5">
          <div class="col-md-4">
            <div class="mb-4">
              <small class="text-label">Patient ID</small>
              <span class="text-value">{{ patient && patient.id }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">Address</small>
              <span class="text-value">{{ patient && patient.address }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">DOB</small>
              <span class="text-value">{{
                patient && patient.dob.toString().split("T")[0]
              }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">Remarks</small>
              <span class="text-value">{{ patient && patient.remarks }}</span>
            </div>
          </div>

          <div class="col-md-4">
            <div class="mb-4">
              <small class="text-label">Name</small>
              <span class="text-value">{{
                patient && patient.firstName + " " + patient.lastName
              }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">NIC</small>
              <span class="text-value">{{ patient && patient.nic }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">Email</small>
              <span class="text-value">{{ patient && patient.email }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">Status</small>
              <span
                *ngIf="patient"
                class="badge bg-success bg-opacity-10 text-success px-3 py-2"
              >
                Active
              </span>
            </div>
          </div>

          <div class="col-md-4">
            <div class="mb-4">
              <small class="text-label">Gender</small>
              <span class="text-value">{{ patient && patient.gender }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">Age</small>
              <span class="text-value">{{ patient && patient.age }}</span>
            </div>
            <div class="mb-4">
              <small class="text-label">Phone Number</small>
              <span class="text-value">{{ patient && patient.contactNo }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
