import { Component, OnInit } from '@angular/core';
import { CommonModule, NgFor } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Medication } from '../../model/medication';
import { FormsModule } from '@angular/forms';
import { Prescription } from '../../model/prescription.model';
import { PrescriptionService } from '../../service/prescription.service';
import { Alert, AlertService } from '../../service/alert.service';
import { AlertComponent } from '../alert/alert.component';
import { Patient } from '../../model/patient.model';
import { Appointment } from '../../model/appointment.model';
import { SearchService } from '../../service/search.service';

@Component({
  selector: 'app-prescription-data-entry',
  standalone: true,
  imports: [CommonModule, FormsModule, AlertComponent],
  templateUrl: './prescription-data-entry.component.html',
  styleUrls: ['./prescription-data-entry.component.css'],
})
export class PrescriptionDataEntryComponent implements OnInit {
  drugData: Medication = new Medication('', '', '');
  alerts: Alert[] = [];
  editingIndex: number | null = null;
  drugsList: Medication[] = [];
  prescription: Prescription = {} as Prescription;
  patient: Patient = {} as Patient;
  currentAppointment: Appointment[] = [];
  appointmentId: number = 0;

  constructor(
    private prescriptionService: PrescriptionService,
    private alertService: AlertService,
    private searchService: SearchService
  ) {}
  ngOnInit(): void {
    this.alertService.alert$.subscribe((alerts) => {
      this.alerts = alerts;
    });

    this.searchService.appointmentData$.subscribe((appointmentData: any) => {
      this.currentAppointment = appointmentData;
    });

    this.searchService.holdPatientData$.subscribe((holdPatientData: any) => {
      if (holdPatientData) {
        this.drugsList = holdPatientData;
      }
    });
  }

  public add(): void {
    if (localStorage.getItem('isPatientSearched') != 'true') {
      this.alertService.showAlert(
        'error',
        'Please search for a patient first.'
      );
      return;
    }

    if (
      !this.drugData.drugName ||
      !this.drugData.dosage ||
      !this.drugData.frequency
    ) {
      this.alertService.showAlert(
        'error',
        'Please fill in all the required fields.'
      );
      return;
    }

    if (this.editingIndex !== null) {
      this.drugsList[this.editingIndex] = { ...this.drugData };
      this.editingIndex = null;
    } else {
      this.drugsList.push({ ...this.drugData });
    }

    this.saveDrugsListToLocalStorage();
    this.drugData = new Medication('', '', '');
  }

  public edit(index: number): void {
    this.drugData = { ...this.drugsList[index] };
    this.editingIndex = index;
  }

  public delete(index: number): void {
    this.drugsList.splice(index, 1);
    this.saveDrugsListToLocalStorage();

    if (this.editingIndex === index) {
      this.drugData = new Medication('', '', '');
      this.editingIndex = null;
    }
  }

  public confirmSavePrescription(): void {
    this.prescription.medications = [...this.drugsList];

    const currentPatient = localStorage.getItem('currentPatient');
    this.patient = currentPatient ? JSON.parse(currentPatient) : null;

    this.prescription = {
      patientId: this.patient.id,
      appointmentId:
        this.currentAppointment[this.currentAppointment.length - 1]
          .appointmentId,
      medications: this.drugsList,
    };

    this.prescriptionService.addPrescription(this.prescription).subscribe(
      (response) => {
        this.drugsList = [];
        this.saveDrugsListToLocalStorage();
        this.drugData = new Medication('', '', '');
        localStorage.removeItem('isPatientSearched');
        localStorage.removeItem('currentPatient');
        this.alertService.showAlert(
          'success',
          'Prescription saved successfully.'
        );
        window.location.reload();
      },
      (error) => {
        this.alertService.showAlert('error', 'Error saving prescription.');
      }
    );
  }

  private saveDrugsListToLocalStorage(): void {
    localStorage.setItem('drugsList', JSON.stringify(this.drugsList));
  }
}
