/* Common styles for appointment components */
.container {
    font-family: 'Poppins', sans-serif;
    padding: 20px;
  }
  
  .page-title {
    font-size: 2rem;
    color: #38823E;
    font-weight: 600;
  }
  
  .breadcrumb {
    font-size: medium;
    color: #6c757d;
    margin-top: 1%;
    text-align: right;
  }
  
  .card {
    border-radius: 0.4rem;
    background-color: #fff;
  }
  
  .custom-shadow {
    box-shadow: 8px 8px 8px rgba(11, 105, 30, 0.2);
  }
  
  .section-container {
    background-color: #dcf4da;
    padding: 20px;
    border-radius: 0.4rem;
    margin-bottom: 20px;
  }
  
  .text-custom-dark {
    color: #0c5e07;
    font-size: 1.2rem;
    font-weight: bold;
  }
  
  .form-control {
    border-color: #80CD84;
    font-size: 0.9rem;
  }
  
  .form-control::placeholder {
    color: #80CD84;
  }
  
  .form-control:focus {
    border-color: #38823E;
    box-shadow: 0 0 0 0.2rem rgba(56, 130, 62, 0.25);
  }
  
  /* Table Styles */
  .table {
    margin-bottom: 0;
  }
  
  .table thead th {
    background-color: #38823E;
    color: white;
    border: none;
    padding: 12px;
  }
  
  .table tbody td {
    padding: 12px;
    border-color: #dcf4da;
  }
  
  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-container {
    background-color: white;
    padding: 2rem;
    border-radius: 0.4rem;
    width: 90%;
    max-width: 500px;
  }
  
  .alert-box {
    text-align: center;
  }
  
  .alert-icon {
    width: 60px;
    height: 60px;
    background-color: #38823E;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1rem;
  }
  
  .alert-icon.warning {
    background-color: #dc3545;
  }
  
  .alert-icon span {
    color: white;
    font-size: 2rem;
  }
  
  .alert-content h2 {
    color: #38823E;
    margin-bottom: 1.5rem;
  }
  
  .alert-details {
    text-align: left;
    margin: 1.5rem 0;
  }
  
  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
  }
  
  .detail-row label {
    color: #38823E;
  }
  
  @media (max-width: 768px) {
    .page-title {
      font-size: 1.5rem;
    }
    
    .section-container {
      padding: 15px;
    }
    
    .modal-container {
      width: 95%;
      padding: 1rem;
    }
  }