*{
  font-size: 0.9rem;
}

.pdf-image{
  width: 45px;
  height: 45px;
  background-color: none;
  background-position: center;
  background-size: cover;
  background-image: url('image/pdf.png');
}

.frame {
  height: 800px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  width: 100vh;
  height: 100vh;
  border-left: 1px solid #054d09;
  border-top: 1px solid #054d09;
  padding: 10px;
  margin-top: 2px;
  border-radius: 17px;
  background-color: #fefefe;
}

.scrollable-container {
  cursor: pointer;
  text-align: left;
  height: 640px;
  width: 330px;
  overflow-y: scroll;
  padding: 10px;
  transition: background-color 0.3s ease;
}
.header-container {
  position: relative;
  display: flex;
  justify-content: left;
  align-items: left;
  margin-bottom: 20px;
  font-weight: bold;
  color: #4cc882;
}

.bi-funnel-fill:hover {
  color: #0c5e07;
  transform: translateY(-50%) scale(1.9);
  transition: all 0.3s ease;
}
.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bi-stop-circle-fill {
  font-size: 3rem;
  cursor: pointer;
  margin-top: -10px;
}
.bi-stop-circle-fill:hover {
  transform: scale(1.2);
  transition: all 0.3s ease;
}
.bi-filetype-pdf {
  padding-left: 15px;
  display: inline-block;
  font-size: 3rem;
  color: #dc3545;
}
.bi-file-earmark-pdf:hover {
  color: #a71d2a;
}

.fulcard {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-right: 30px;
}
.frame {
  height: 800px;
  width: 350px;
  padding: 10px;
  margin-top: 2px;
  border-radius: 8px;
  background-color: #fefefe;
  box-shadow: 9px 9px 9px rgba(0, 0, 0, 0.1);
}

.card-shadow{
  color: #45a049;
  transition: all 0.3s ease;
  border-radius: 10px;
}

.card-shadow:hover {
  transform:  scale(1.03);
  background-color: #dcf4da;
}

.scrollable-container {
  cursor: pointer;
  text-align: left;
  height: 640px;
  width: 330px;
  overflow-y: scroll;
  padding: 10px;
  transition: background-color 0.3s ease;
}

.header-container {
  position: relative;
  display: flex;
  justify-content: left;
  align-items: left;
  margin-bottom: 20px;
  font-weight: bold;
  color: #45a049;
}

.bi-funnel-fill {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}

.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stop-circle-fill {
  cursor: pointer;
  margin-left: auto;
  color: #4caf50;
}

.frame {
  height: 800px;
  box-shadow: 9px 9px 9px rgba(0.3, 0.3, 0.3, 0.3);
  width: 350px;
  padding: 10px;
  margin-top: 2px;
  border-radius: 17px;
  background-color: #fefefe;
}

.scrollable-container {
  cursor: pointer;
  text-align: left;
  height: 640px;
  width: 330px;
  overflow-y: scroll;
  padding: 10px;
  transition: background-color 0.3s ease;
}
.header-container {
  position: relative;
  display: flex;
  justify-content: left;
  align-items: left;
  margin-bottom: 20px;
  font-weight: bold;
  color: #4cc882;
}
.bi-funnel-fill {
  font-size: 2.5rem;
  position: absolute;
  top: 18px;
  right: 10px;
  cursor: pointer;
  color: #4caf50;
  transform: translateY(-50%);
}

.bi-funnel-fill:hover {
  color: #45a049;
  transform: translateY(-50%) scale(1.3);
  transition: all 0.3s ease;
}
.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bi-stop-circle-fill {
  font-size: 3rem;
  cursor: pointer;
  margin-top: -10px;
  color: #4caf50;
}
.bi-stop-circle-fill:hover {
  color: #45a049;
  transform: scale(1.2);
  transition: all 0.3s ease;
}
.bi-filetype-pdf {
  padding-left: 15px;
  display: inline-block;
  font-size: 3rem;
  color: #dc3545;
}
.bi-file-earmark-pdf:hover {
  color: #a71d2a;
}
.fulcard {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-right: 30px;
}

.alerts-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 1000; 
}

.report-category{
  font-size: 0.8rem;
  color: #45a049;
}

/* Hold Prescription */
.modal-content {
  font-family: 'Poppins', sans-serif;
}

.btn btn-success:hover {
  background-color: #003400;
}

