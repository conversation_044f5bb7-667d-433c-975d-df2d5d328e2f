.container {
    font-family: 'Poppins', sans-serif;
    padding: 20px;
  }
  
  .page-title {
    font-size: 2rem;
    color: #38823E;
    font-weight: 600;
  }
  
  .breadcrumb {
    font-size: medium;
    color: #6c757d;
    margin-top: 1%;
    text-align: right;
  }
  
  .card {
    border-radius: 0.4rem;
    background-color: #fff;
  }
  
  .custom-shadow {
    box-shadow: 8px 8px 8px rgba(11, 105, 30, 0.2);
  }
  
  .section-container {
    background-color: #dcf4da;
    padding: 20px;
    border-radius: 0.4rem;
    margin-bottom: 20px;
  }
  
  .text-custom-dark {
    color: #0c5e07;
    font-size: 1.2rem;
    font-weight: bold;
  }
  
  .text-custom-green {
    color: #38823E;
    font-weight: 500;
  }
  
  .form-control {
    border-color: #80CD84;
    font-size: 0.9rem;
  }
  
  .form-control::placeholder {
    color: #80CD84;
  }
  
  .form-control:focus {
    border-color: #38823E;
    box-shadow: 0 0 0 0.2rem rgba(56, 130, 62, 0.25);
  }
  
  .search-bar {
    position: relative;
    width: 100%;
  }
  
  .search-input {
    padding-right: 40px;
    border-radius: 20px;
  }
  
  .search-btn {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    border: none;
    background: transparent;
    padding: 0 15px;
    color: #38823E;
  }
  
  .search-btn:hover {
    color: #0c5e07;
  }
  
  .table {
    margin-bottom: 0;
  }
  
  .table th {
    border-bottom: 2px solid #38823E;
    white-space: nowrap;
  }
  
  .table td {
    vertical-align: middle;
  }
  
  .btn-outline-success {
    color: #38823E;
    border-color: #38823E;
  }
  
  .btn-outline-success:hover {
    background-color: #38823E;
    color: #fff;
  }
  
  .text-label {
    color: #38823E;
    font-weight: 600;
    margin-right: 8px;
  }
  
  .text-value {
    color: #333;
  }
  
  .badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
  }
  
  @media (max-width: 768px) {
    .page-title {
      font-size: 1.5rem;
    }
    
    .section-container {
      padding: 15px;
    }
  }