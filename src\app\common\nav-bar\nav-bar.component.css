@import 'bootstrap-icons/font/bootstrap-icons.css';

:host {
  display: block;
  height: 100%;
}

.sidebar {
  width: 280px;
  height: 100%;
  background-color: #ffffff;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar.collapsed .logo-text {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  display: none;
}

.sidebar.collapsed .menu-link span {
  display: none;
}

.sidebar.collapsed .submenu {
  display: none;
}

.logo-container {
  padding: 1rem;
  background: linear-gradient(135deg, #c7d9c2, #2d9035);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #fff;
  gap: 0.5rem;
}

.logo-image {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 600;
  white-space: nowrap;
}

.collapse-btn {
  background: transparent;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.menu-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  margin: 0.25rem 0;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #38823e;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  gap: 1rem;
  border-radius: 0 25px 25px 0;
  margin-right: 1rem;
}

.menu-link:hover {
  background-color: rgba(56, 130, 62, 0.1);
}

.menu-link.active {
  background-color: #38823e;
  color: #fff;
}

.menu-link i {
  font-size: 1.2rem;
  min-width: 24px;
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.submenu.expanded {
  max-height: 500px;
}

.submenu a {
  display: block;
  padding: 0.5rem 1rem 0.5rem 3.7rem;
  color: #38823e;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.submenu a:hover {
  background-color: rgba(56, 130, 62, 0.1);
}

.submenu a.active {
  background-color: #dcf4da;
  font-weight: 500;
}

.bottom-menu {
  margin-top: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1rem;
}

.bi-chevron-down {
  transition: transform 0.3s ease;
}

.bi-chevron-down.rotated {
  transform: rotate(180deg);
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }
}

.hide-text {
  display: none;
}
