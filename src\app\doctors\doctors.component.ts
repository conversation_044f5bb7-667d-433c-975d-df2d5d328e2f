import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-doctors',
  standalone: true,
  imports: [CommonModule, FormsModule,RouterLink],
  templateUrl: './doctors.component.html',
  styleUrls: ['./doctors.component.css']
})
export class DoctorsComponent {
  doctors = [
    {
      "name": "Dr. <PERSON><PERSON><PERSON>",
      "regNo": "Rf00001",
      "contact": "+94 77 988 2060",
      "email": "<EMAIL>",
      "profileImage": "https://images.squarespace-cdn.com/content/v1/54d50ceee4b05797b34869cf/1667588293462-G95L6HEPY5UGIH6FISP4/bigstock-Doctor-physician--Isolated-ov-33908342.jpg?format=750w"
    },
    {
      "name": "Dr. <PERSON>",
      "regNo": "Rf00002",
      "contact": "+94 77 988 2070",
      "email": "dr<PERSON><EMAIL>",
      "profileImage": "https://mcc.ca/wp-content/uploads/doctor-hero-1.png"
    },
    {
      "name": "Dr. <PERSON>",
      "regNo": "Rf00003",
      "contact": "+94 77 988 2080",
      "email": "<EMAIL>",
      "profileImage": "https://doctorsonduty.com/wp-content/uploads/2023/11/accent1.png"
    },
    {
      "name": "Dr. Bob",
      "regNo": "Rf00004",
      "contact": "+94 77 988 2090",
      "email": "<EMAIL>",
      "profileImage": "https://plushcare.com/hs-fs/hubfs/Headshots/Doctor%20Headshots/Allan%20Marks.webp?width=1000&height=500&name=Allan%20Marks.webp"
    },
    {
      "name": "Dr. Sarah",
      "regNo": "Rf00005",
      "contact": "+94 77 988 2100",
      "email": "<EMAIL>",
      "profileImage": "https://media.cnn.com/api/v1/images/stellar/prod/230217092727-02-us-black-doctors-shortage.jpg?c=original"
    },
    {
      "name": "Dr. Emma",
      "regNo": "Rf00006",
      "contact": "+94 77 988 2110",
      "email": "<EMAIL>",
      "profileImage": "https://epmgaa.media.clients.ellingtoncms.com/img/photos/2023/02/23/Screenshot_2023-02-23_at_9.57.20_AM_t750x550.png?d885fc46c41745b3b5de550c70336c1b382931d2"
    },
    {
      "name": "Dr. Dave",
      "regNo": "Rf00007",
      "contact": "+94 77 988 2120",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Olivia",
      "regNo": "Rf00008",
      "contact": "+94 77 988 2130",
      "email": "<EMAIL>",
      "profileImage": "https://www.pcom.edu/_resources/images/digest/winter-2021/AshleyPeterson_profile.jpg"
    },
    {
      "name": "Dr. James",
      "regNo": "Rf00009",
      "contact": "+94 77 988 2140",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Lily",
      "regNo": "Rf00010",
      "contact": "+94 77 988 2150",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Mike",
      "regNo": "Rf00011",
      "contact": "+94 77 988 2160",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Lily-Anne",
      "regNo": "Rf00012",
      "contact": "+94 77 988 2170",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Peter",
      "regNo": "Rf00013",
      "contact": "+94 77 988 2180",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Noah",
      "regNo": "Rf00014",
      "contact": "+94 77 988 2190",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Mia",
      "regNo": "Rf00015",
      "contact": "+94 77 988 2200",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Ben",
      "regNo": "Rf00016",
      "contact": "+94 77 988 2210",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Eve",
      "regNo": "Rf00017",
      "contact": "+94 77 988 2220",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Ryan",
      "regNo": "Rf00018",
      "contact": "+94 77 988 2230",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Chloe",
      "regNo": "Rf00019",
      "contact": "+94 77 988 2240",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    },
    {
      "name": "Dr. Ethan",
      "regNo": "Rf00020",
      "contact": "+94 77 988 2250",
      "email": "<EMAIL>",
      "profileImage": "https://static.vecteezy.com/system/resources/thumbnails/026/375/249/small_2x/ai-generative-portrait-of-confident-male-doctor-in-white-coat-and-stethoscope-standing-with-arms-crossed-and-looking-at-camera-photo.jpg"
    }
  ];
}
