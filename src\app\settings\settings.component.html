<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Settings</p>
    </div>
    <div class="col-7"></div>
    <div class="col-1">
      <p class="breadcrumb">Home/Settings</p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <div class="p-4">
          <!-- Navigation Tabs -->
          <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'profile'" (click)="activeTab = 'profile'">Profile</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'security'" (click)="activeTab = 'security'">Security</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'notifications'" (click)="activeTab = 'notifications'">Notifications</a>
            </li>
          </ul>

          <!-- Security Settings -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Security Settings</h3>
            <p class="text-muted">These settings help you keep your account secure.</p>
            
            <div class="list-group">
              <div class="list-group-item bg-light border-0 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 class="mb-1">Enable Activity Logs</h5>
                    <p class="text-muted mb-0">Track all activities in your account</p>
                  </div>
                  <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="activityLog" checked>
                  </div>
                </div>
              </div>

              <div class="list-group-item bg-light border-0 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 class="mb-1">Two-Factor Authentication</h5>
                    <p class="text-muted mb-0">Additional security for your account</p>
                  </div>
                  <button class="btn btn-success">Enable</button>
                </div>
              </div>

              <div class="list-group-item bg-light border-0">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 class="mb-1">PIN Code Protection</h5>
                    <p class="text-muted mb-0">Secure access with PIN</p>
                  </div>
                  <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="pinCode">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="section-container">
            <h3 class="text-custom-dark mb-3">Recent Activity</h3>
            <p class="text-muted">Last activities with your account</p>
            
            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th>Device</th>
                    <th>Location</th>
                    <th>IP Address</th>
                    <th>Time</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><i class="bi bi-laptop me-2"></i>Chrome - Windows 10</td>
                    <td>Paris, France</td>
                    <td>************</td>
                    <td>Apr 24, 2023</td>
                    <td><i class="bi bi-x-circle text-danger"></i></td>
                  </tr>
                  <!-- Add more rows as needed -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>