import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppoimentSearchComponent } from '../appoiment-search/appoiment-search.component';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { HeaderAdminSectionComponent } from '../common/header-admin-section/header-admin-section.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-appointment-view',
  standalone: true,
  imports: [CommonModule, HttpClientModule, FormsModule, HeaderAdminSectionComponent, RouterLink],
  templateUrl: './appointment-view.component.html',
   styleUrl: './appointment-view.component.css'
})
export class AppointmentViewComponent {

  currentDate = new Date();
  
  public appointmentList: any[] = [];
  public searchPatientId: string = ''; 

  constructor(private http: HttpClient) {
    this.loadTable();
  }

  loadTable(): void {
    this.http.get("http://localhost:8080/appointment/get-appointment-list").subscribe(
      (data: any) => {
        console.log(data);
        this.appointmentList = data;
      },
      (error) => {
        console.error('Error loading appointments:', error);
        alert('Failed to load appointments.');
      }
    );
  }

  // Search appointments by Patient ID
  searchByPatientId(): void {
    if (this.searchPatientId.trim() === '') {
      alert('Please enter a valid Patient ID.');
      return;
    }

    const apiUrl = `http://localhost:8080/appointment/search-by-patientId/${this.searchPatientId}`;

    this.http.get(apiUrl).subscribe(
      (data: any) => {
        console.log(data);
        this.appointmentList = Array.isArray(data) ? data : [data]; // Ensure data is an array
      },
      (error) => {
        console.error('Error searching appointments:', error);
        alert('No appointments found for the given Patient ID.');
      }
    );
  }
}
