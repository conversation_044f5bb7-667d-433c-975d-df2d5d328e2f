import { CommonModule, NgClass } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-alert',
  standalone: true,
  imports: [FormsModule, CommonModule, NgClass],
  templateUrl: './alert.component.html',
  styleUrl: './alert.component.css'
})
export class AlertComponent implements OnInit {
  @Input() alert: Alert = {
    type: '',
    message: ''
  };

  alertClasses: string = '';

  ngOnInit(): void {
    this.setAlertClasses();
  }

  setAlertClasses() {
    switch (this.alert.type) {
      case 'success':
        this.alertClasses = 'alert-success';
        break;
      case 'error':
        this.alertClasses = 'alert-danger';
        break;
      case 'info':
        this.alertClasses = 'alert-info';
        break;
      default:
        this.alertClasses = 'alert-secondary';
        break;
    }
  }
}

interface Alert {
  type: string;
  message: string;
}