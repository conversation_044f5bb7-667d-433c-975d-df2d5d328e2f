import { CommonModule, NgFor } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ReportService } from '../../service/reportsAndRecords/report.service';
import { Report } from '../../service/reportsAndRecords/report-record-env.service';
import { FormsModule, NgForm } from '@angular/forms';
import { RecordService } from '../../service/reportsAndRecords/record.service';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-add-records',
  standalone: true,
  imports: [NgFor, FormsModule, CommonModule, RouterLink],
  templateUrl: './add-records.component.html',
  styleUrl: './add-records.component.css',
})
export class AddRecordsComponent implements OnInit {

  //Records section
  public addedReportList: Report[] = [];
  public reportNames: string = '';
  public prescriptionList: any[] = [];
  public prescriptionNames: string = '';
  public recordId: any;
  public searchedPatient: any = null;
  public currentRecord: any = null;

  public allReportList!: Report[];
  public allPrescriptionList = [
    {
      id: 'Preid1',
      name: 'Amlodipine 5mg',
      date: '2024-10-12',
      link: '/resource/Amlodipine_5mg.pdf',
      isChecked: false
    },
    {
      id: 'Preid2',
      name: 'Metformin 550g',
      date: '2024-10-10',
      link: '/resource/Metformin_500mg.pdf',
      isChecked: false
    },
    {
      id: 'Preid3',
      name: 'Levothyroxine 50mcg',
      date: '2024-10-11',
      link: '/resource/Levothyroxine_50mcg.pdf',
      isChecked: false
    },
    {
      id: 'Preid4',
      name: 'Atorvastatin 20mg',
      date: '2024-10-12',
      link: '/resource/Atorvastatin_20mg.pdf',
      isChecked: false
    },
    {
      id: 'Preid5',
      name: 'Omeprazole 20mg',
      date: '2024-10-13',
      link: '/resource/Omeprazole_20mg.pdf',
      isChecked: false
    },
    {
      id: 'Preid6',
      name: 'Sertraline 50mg',
      date: '2024-10-12',
      link: '/resource/Sertraline_50mg.pdf',
      isChecked: false
    },
    {
      id: 'Preid7',
      name: 'Albuterol Inhaler',
      date: '2024-10-10',
      link: '/resource/Albuterol_Inhaler.pdf',
      isChecked: false
    },
    {
      id: 'Preid8',
      name: 'Lisinopril 10mg',
      date: '2024-10-13',
      link: '/resource/Lisinopril_10mg.pdf',
      isChecked: false
    }
  ];

  // Add a patient details array
  public patients = [
    {
      id: 1,
      name: 'Sachin Prabshwara',
      gender: 'Male',
      age: 25,
      contact: '************',
    },
    {
      id: 2,
      name: 'Nisila Perera',
      gender: 'male',
      age: 20,
      contact: '************',
    },
    {
      id: 3,
      name: 'Sadeepa Hearath',
      gender: 'Male',
      age: 20,
      contact: '************',
    },
    {
      id: 4,
      name: 'Namal Rajapaksha',
      gender: 'Male',
      age: 35,
      contact: '************',
    },
    {
      id: 5,
      name: 'Kusal Medndis',
      gender: 'Male',
      age: 19,
      contact: '************',
    },
  ];
  displayReportList!: Report[];
  recordDescription: string = "Detailed medical record";
  patientIdToPayLoad!: string;


  constructor(
    private http: HttpClient,
    private reportService: ReportService,
    private recordService: RecordService
  ) {
    this.loadReports()
  }

  ngOnInit(): void {
    this.clearAllFields();
    this.currentRecord = this.recordService.getCurrentRecord();

    if (this.currentRecord === null) {
      this.http
        .get('http://localhost:8083/record/get-nextId', { responseType: 'text' })
        .subscribe({
          next: (response: string) => {
            this.recordId = response;
          },
          error: (err) => console.error('Failed to fetch record ID:', err),
        });
    } else {
      console.log('Current record:', this.currentRecord);

      if (this.currentRecord.patientID) {
        this.searchedPatient = this.currentRecord.patientID;
        this.patientIdToPayLoad = this.currentRecord.patientID;
        this.searchPatientById(this.patientIdToPayLoad);
      } else {
        console.error('Current record does not have a valid patientId');
      }

      this.recordId = this.currentRecord.recordId || '';
      this.prescriptionList = this.currentRecord.reportList || [];
      this.addedReportList = this.currentRecord.reportList || [];
      this.recordDescription = this.currentRecord.description || '';
    }
  }

  //For storing display Reports and Prescriptions
  // public displayReportList = this.allReportList;
  public displayPrescriptionList = this.allPrescriptionList;

  searchReportsByDate(event: Event) {
    let date: string = (event.target as HTMLInputElement).value;
    if (date.length > 0) {
      //Change display lists to filtered by date list
      this.displayReportList = this.allReportList.filter(
        (element) => element.date == date
      );
      this.displayPrescriptionList = this.allPrescriptionList.filter(
        (element) => element.date == date
      );
    } else {
      //Display lists back to all list
      this.displayReportList = this.allReportList;
      this.displayPrescriptionList = this.allPrescriptionList;
    }
  }


  loadReports() {
    this.reportService.getAllReports().subscribe({
      next: data => {
        this.allReportList = data.map(report => ({
          ...report,
          isChecked: false
        }));
      }
    })

  }

  addReport(inComingReport: Report, event: Event) {
    if (this.addedReportList.includes(inComingReport)) {
      alert("report is already added")
    } else {
      this.addedReportList.push(inComingReport)
    }
  }

  removeFromAddedReports(report: Report) {
    const index = this.addedReportList.indexOf(report);
    if (index > -1) {
      this.addedReportList.splice(index, 1);
    }
    this.removeCheckFromAllList(report.reportId)
  }
  //removing check from the all report/prescription list just after clinking remove (x) button in the added report list/added prescription list;
  removeCheckFromAllList(reportId: string) {
    const element = document.getElementById(reportId);
    if (element) {
      const checkbox = element.querySelector('input[type="checkbox"]') as HTMLInputElement;
      if (checkbox) {
        checkbox.checked = false; // Uncheck the checkbox
        // Update the corresponding object's `isChecked` property
        const report = this.allReportList.find(r => r.reportId === reportId);
        if (report) {
          report.isChecked = false;
        }
      }
    }
  }
  //changes added again

  submitCreateRecordForm(form: NgForm) {
    console.log(form.value);
  }

  addPrescription(prescription: any, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (isChecked) {
      // Add the prescription to the list
      this.prescriptionList.push(prescription);
      this.prescriptionNames += prescription.name + '\n';
    } else {
      // Remove the prescription from the list
      this.prescriptionList = this.prescriptionList.filter(
        (p) => p.name !== prescription.name
      );

      this.prescriptionNames = this.prescriptionList
        .map((p) => p.name)
        .join('\n');
    }
  }

  removeFromPrescriptions(prescription: any) {
    const index = this.prescriptionList.indexOf(prescription);
    if (index > -1) {
      this.prescriptionList.splice(index, 1);
    }
    this.removeCheckFromAllList(prescription.id)
  }

  parseStringToId(formattedId: string): Number {
    if (!formattedId.startsWith('P') || formattedId.length < 5) {
      throw new Error("Invalid format. Expected format is 'Pxxxx'.");
    }
    return Number(formattedId.slice(1));
  }

  searchPatientById(patientId: string) {
    if (!patientId.trim()) {
      alert('Please enter a Patient ID.');
      return;
    }

    let id: Number;

    try {
      id = this.parseStringToId(patientId.trim());
    } catch (error) {
      alert('Invalid Patient ID format. Please check and use like "Pxxxx".');
      return;
    }

    const patient = this.patients.find((p) => p.id === id);

    if (patient) {
      this.searchedPatient = patient;
      document.querySelector('.patient-info')?.removeAttribute('style');
    } else {
      alert('Patient not found.');
      this.searchedPatient = null;
      document.querySelector('.patient-info')?.setAttribute('style', 'display: none;');
    }
  }
  generateRandomId() {
    return Math.floor(Math.random() * (100000 - 0 + 1)) + 1000;
  }
  saveRecord() {
    if (this.addedReportList.length <= 0) {
      alert("please add reports to the record...!");
      return;
    }
    if (this.patientIdToPayLoad = "") {
      alert("please add patient ID...!");
      return;
    }

    const payload = {
      recordId: "R" + this.generateRandomId(),
      patientID: this.patientIdToPayLoad ? this.patientIdToPayLoad : this.addedReportList[0].patientId,
      recordDate: new Date().toISOString().split('T')[0],
      description: this.recordDescription,
      reportList: this.addedReportList
    };

    console.log('Sending payload:', payload); // test log

    if (this.currentRecord === null) {

      this.http
        .post('http://localhost:8083/record/add-record', payload)
        .subscribe({
          next: (response) => {
            alert('Record saved successfully: ' + response);
            this.clearAllFields();
          },
          error: (error: HttpErrorResponse) => {
            if (error.status === 200) {
              alert('Record saved successfully: ');
              this.clearAllFields();
            } else {
              alert("Error Saving Record :" + error.message);
            }
          },
        });
    } else {
      this.http
        .put('http://localhost:8083/update-record', payload)
        .subscribe({
          next: (response) => {
            alert('Record updated successfully: ' + response);
            this.clearAllFields();
          },
          error: (error: HttpErrorResponse) => {
            alert("Error Updating Record: " + error.message);
          },
        });
    }

  }

  clearCheckBox() {
    this.prescriptionList.forEach(prescription => prescription.isChecked = false);
    this.addedReportList.forEach(report => report.isChecked = false);
  }

  clearAllFields() {
    this.clearCheckBox();
    this.addedReportList = [];
    this.prescriptionList = [];
    this.reportNames = '';
    this.prescriptionNames = '';
    (document.getElementById('note') as HTMLTextAreaElement).value = '';
    this.searchedPatient = null;
    this.recordDescription = "Detailed medical record";
  }
}