import { Component } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

interface BasePatientData {
  firstName: string;
  lastName: string;
  age: number | null;
  dob: string;
  nic: string;
  contactNo: string;
  email: string;
  address: string;
  gender: string;
  bloodGroup: string;
  patientStatus: boolean;
  allergyStatus: boolean;
  remarks: string;
  admittedDateTime: string;
}

interface PatientWithGuardian extends BasePatientData {
  guardianNIC: string;
  guardianName: string;
  guardianContact: string;
}

type PatientData = BasePatientData | PatientWithGuardian;

@Component({
  selector: 'app-add',
  templateUrl: './add.component.html',
  styleUrls: ['./add.component.css'],
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink]
})
export class AddComponent {
  private API_URL = `${environment.PATIENT_MANAGEMENT_BASE_URL}/add-patient`;
  showSuccessModal = false;
  showErrorModal = false;
  errorMessage = '';

  patient: PatientWithGuardian = {
    firstName: '',
    lastName: '',
    age: null,
    dob: '',
    nic: '',
    contactNo: '',
    email: '',
    address: '',
    gender: '',
    guardianNIC: '',
    guardianName: '',
    guardianContact: '',
    bloodGroup: '',
    patientStatus: false,
    allergyStatus: false,
    remarks: '',
    admittedDateTime: new Date().toISOString()
  };

  hasGuardian = false;
  errors: { [key: string]: string } = {};
  genderTypes = ['Male', 'Female', 'Other'];
  bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

  constructor(private http: HttpClient) {}

  validateForm(): boolean {
    this.errors = {};
    let isValid = true;

    // Required fields validation
    if (!this.patient.firstName?.trim()) {
      this.errors['firstName'] = 'First name is required';
      isValid = false;
    }

    if (!this.patient.lastName?.trim()) {
      this.errors['lastName'] = 'Last name is required';
      isValid = false;
    }

    if (!this.patient.age || this.patient.age <= 0) {
      this.errors['age'] = 'Valid age is required';
      isValid = false;
    }

    if (!this.patient.dob) {
      this.errors['dob'] = 'Date of birth is required';
      isValid = false;
    }

    if (!this.patient.nic?.trim()) {
      this.errors['nic'] = 'NIC is required';
      isValid = false;
    } else if (!this.validateNIC(this.patient.nic)) {
      this.errors['nic'] = 'Invalid NIC format';
      isValid = false;
    }

    if (!this.patient.contactNo?.trim()) {
      this.errors['contactNo'] = 'Contact number is required';
      isValid = false;
    } else if (!this.validatePhone(this.patient.contactNo)) {
      this.errors['contactNo'] = 'Invalid contact number format';
      isValid = false;
    }

    if (this.patient.email && !this.validateEmail(this.patient.email)) {
      this.errors['email'] = 'Invalid email format';
      isValid = false;
    }

    if (!this.patient.gender) {
      this.errors['gender'] = 'Gender is required';
      isValid = false;
    }

    if (!this.patient.address?.trim()) {
      this.errors['address'] = 'Address is required';
      isValid = false;
    } else if (this.patient.address.trim().length < 10) {
      this.errors['address'] = 'Address must be at least 10 characters long';
      isValid = false;
    }

    if (!this.patient.bloodGroup) {
      this.errors['bloodGroup'] = 'Blood group is required';
      isValid = false;
    }

    if (this.hasGuardian) {
      if (!this.patient.guardianNIC?.trim()) {
        this.errors['guardianNIC'] = 'Guardian NIC is required';
        isValid = false;
      } else if (!this.validateNIC(this.patient.guardianNIC)) {
        this.errors['guardianNIC'] = 'Invalid Guardian NIC format';
        isValid = false;
      }

      if (!this.patient.guardianName?.trim()) {
        this.errors['guardianName'] = 'Guardian name is required';
        isValid = false;
      }

      if (!this.patient.guardianContact?.trim()) {
        this.errors['guardianContact'] = 'Guardian contact is required';
        isValid = false;
      } else if (!this.validatePhone(this.patient.guardianContact)) {
        this.errors['guardianContact'] = 'Invalid guardian contact format';
        isValid = false;
      }
    }

    return isValid;
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    return emailRegex.test(email);
  }

  validatePhone(phone: string): boolean {
    const phoneRegex = /^[0-9]{10}$/;
    return phoneRegex.test(phone);
  }

  validateNIC(nic: string): boolean {
    const oldNICRegex = /^[0-9]{9}[VvXx]$/;
    const newNICRegex = /^[0-9]{12}$/;
    return oldNICRegex.test(nic) || newNICRegex.test(nic);
  }

  preparePatientData(): any {
    const baseData = {
      firstName: this.patient.firstName.trim(),
      lastName: this.patient.lastName.trim(),
      age: this.patient.age,
      dob: this.patient.dob,
      nic: this.patient.nic.trim(),
      contactNo: this.patient.contactNo.trim(),
      email: this.patient.email.trim(),
      address: this.patient.address.trim(),
      gender: this.patient.gender,
      bloodGroup: this.patient.bloodGroup,
      patientStatus: this.patient.patientStatus.toString(),
      allergyStatus: this.patient.allergyStatus.toString(),
      remarks: this.patient.remarks.trim(),
      admittedDateTime: this.patient.admittedDateTime
    };

    if (this.hasGuardian) {
      return {
        ...baseData,
        guardianNIC: this.patient.guardianNIC.trim(),
        guardianName: this.patient.guardianName.trim(),
        guardianContact: this.patient.guardianContact.trim()
      };
    }

    return baseData;
  }

  onSubmit() {
    if (this.validateForm()) {
      const headers = new HttpHeaders().set('Content-Type', 'application/json');
      const patientData = this.preparePatientData();

      this.http.post(this.API_URL, patientData, { headers, responseType: 'text' })
        .subscribe({
          next: () => {
            this.showSuccessModal = true;
          },
          error: (error: HttpErrorResponse) => {
            this.errorMessage = 'Failed to add patient. Please try again later.';
            if (error.error && typeof error.error === 'string') {
              this.errorMessage = error.error;
            }
            this.showErrorModal = true;
          }
        });
    }
  }

  closeSuccessModal() {
    this.showSuccessModal = false;
    this.resetForm();
  }

  closeErrorModal() {
    this.showErrorModal = false;
    this.errorMessage = '';
  }

  resetForm() {
    this.patient = {
      firstName: '',
      lastName: '',
      age: null,
      dob: '',
      nic: '',
      contactNo: '',
      email: '',
      address: '',
      gender: '',
      guardianNIC: '',
      guardianName: '',
      guardianContact: '',
      bloodGroup: '',
      patientStatus: false,
      allergyStatus: false,
      remarks: '',
      admittedDateTime: new Date().toISOString()
    };
    this.hasGuardian = false;
    this.errors = {};
  }

  hasError(field: string): boolean {
    return this.errors.hasOwnProperty(field);
  }

  getError(field: string): string {
    return this.errors[field];
  }
}