import { Routes } from '@angular/router';
import { DashboardComponent } from './dashboard/dashboard.component';
import { DashBordPageComponent } from './dash-bord-page/dash-bord-page.component';

export const routes: Routes = [
    {
        path: '',
        component: DashBordPageComponent,
        children: [
            {
                path: "view-patient",
                loadComponent: () => import('./patient-management/view-patient-list/view-patient-list.component').then(m => m.ViewPatientListComponent)
            },
            {
                path: "search-patient",
                loadComponent: () => import('./patient-management/search/search.component').then(m => m.SearchComponent)
            },
            {
                path: "add-patient",
                loadComponent: () => import('./patient-management/add/add.component').then(m => m.AddComponent)
            },
            {
                path: "update-patient",
                loadComponent: () => import('./patient-management/update/update.component').then(m => m.UpdateComponent)
            },
            {
                path: "add-appointment",
                loadComponent: () => import('./appointment-handling/add-appointment/add-appointment.component').then(m => m.AddAppointmentComponent)
            },
            {
                path: "delete-appointment",
                loadComponent: () => import('./appointment-handling/delete-appointment/delete-appointment.component').then(m => m.DeleteAppointmentComponent)
            },
            {
                path: "update-appointment",
                loadComponent: () => import('./appointment-handling/update-appointment/update-appointment.component').then(m => m.UpdateAppointmentComponent)
            },
            {
                path: "view-appointment",
                loadComponent: () => import('./appointment-handling/appointment-view/appointment-view.component').then(m => m.AppointmentViewComponent)
            },
            {
                path: "search-appointment",
                loadComponent: () => import('./appointment-handling/appoiment-search/appoiment-search.component').then(m => m.AppoimentSearchComponent)
            },
            {
                path: "add-category",
                loadComponent: () => import('./appointment-handling/add-category/add-category.component').then(m => m.AddCategoryComponent)
            },
            {
                path: "update-category",
                loadComponent: () => import('./appointment-handling/update-category/update-category.component').then(m => m.UpdateCategoryComponent)
            },
            {
                path: "view-category",
                loadComponent: () => import('./appointment-handling/view-category/view-category.component').then(m => m.ViewCategoryComponent)
            },
            {
                path: "view-records",
                loadComponent: () => import('./medical-records-handling/view-records/view-records.component').then(m => m.ViewRecordsComponent)
            },
            {
                path: "add-records",
                loadComponent: () => import('./medical-records-handling/add-records/add-records.component').then(m => m.AddRecordsComponent)
            },
            {
                path: "view-reports",
                loadComponent: () => import('./medical-records-handling/view-reports/view-reports.component').then(m => m.ViewReportsComponent)
            },
            {
                path: "prescription-management",
                loadComponent: () => import('./prescription-management/prescription-management.component').then(m => m.PrescriptionManagementComponent)
            },
            {
                path: "view-doctors",
                loadComponent: () => import('./doctors/doctors.component').then(m => m.DoctorsComponent)
            },
            {
                path: "settings",
                loadComponent: () => import('./settings/settings.component').then(m => m.SettingsComponent)
            },
            {
                path: "",
                component: DashboardComponent
            },
        ]
    },
];
