import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DynamicImportService {

  constructor() { }

  async loadChartJs() {
    try {
      const { default: Chart } = await import('chart.js/auto');
      return Chart;
    } catch (error) {
      console.error('Error loading Chart.js:', error);
      throw error;
    }
  }

  async loadJsPDF() {
    try {
      const { default: jsPDF } = await import('jspdf');
      return jsPDF;
    } catch (error) {
      console.error('Error loading jsPDF:', error);
      throw error;
    }
  }

  async loadHtml2Canvas() {
    try {
      const html2canvas = await import('html2canvas');
      return html2canvas.default;
    } catch (error) {
      console.error('Error loading html2canvas:', error);
      throw error;
    }
  }

  async loadSweetAlert2() {
    try {
      const Swal = await import('sweetalert2');
      return Swal.default;
    } catch (error) {
      console.error('Error loading SweetAlert2:', error);
      throw error;
    }
  }

  async loadBootstrapJS() {
    try {
      await import('bootstrap/dist/js/bootstrap.bundle.min.js');
      return true;
    } catch (error) {
      console.error('Error loading Bootstrap JS:', error);
      throw error;
    }
  }
}
