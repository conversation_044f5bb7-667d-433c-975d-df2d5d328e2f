@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');
*{
  font-family: 'Poppins', sans-serif;
}
.profile-img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #e7e4e4;
  background-position: center;
  background-size: cover;
}

.card {
  border-radius: 0.4rem;
  background-color: #dcf4da;
}
.text-label {
  display: block;
  color: #38823E !important;
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 4px;
}
.sub{
  font-size: 10px;
}
.text-value {
  color: #134b15;
  font-size: 10px;
  font-weight: bold;
  display: block;
}

.badge.bg-success {
  background-color: rgba(56, 130, 62, 0.1) !important;
}

.badge.text-success {
  color: #38823E !important;
}
@media (max-width: 768px) {
  .profile-img {
      width: 80px;
      height: 80px;
  }
}
.btn-outline-custom {
  background-color: white;
  color: #38823E;
  border: 2px solid #0c5e07;
  transition: all 0.3s ease;
}

.btn-outline-custom:hover {
  background-color: #0c5e07;
  color: white;
  border: 2px solid #0c5e07;
}
.nogender{
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #e7e4e4;
  background-position: center;
  background-size: cover;
  background-image: url('image/man.png'); 
}
.man {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #e7e4e4;
  background-position: center;
  background-size: cover;
  background-image: url('image/man.png'); 
}

.alerts-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 1000; 
}

@media (max-width: 768px) {
  .man {
      width: 80px;
      height: 80px;
  }
}
.women {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #e7e4e4;
  background-position: center;
  background-size: cover;
  background-image: url('image/women.png'); 
}
@media (max-width: 768px) {
  .women {
      width: 80px;
      height: 80px;
  }
}

.text-custom-dark {
  color: #0c5e07;
  font-size: 20px;
  font-weight: bold;
}

.text-custom-green {
  font-weight: bold;
  color: #38823E;
}

.btn-custom-green {
  background-color: #136e0e;
  color: white;
  border: none;
  transition: background-color 0.3s ease;
}

.btn-custom-green:hover {
  background-color: #80CD84;
  color: white;
}

.custom-button {
  background-color: #dcf4da;
  min-height: 30px; 
  width: 220px;    
  color: #136e0e 
}
.custom-shadow {
  box-shadow: 8px 8px 8px 8px rgba(11, 105, 30, 0.2); 
}

.search-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.search-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  font-size: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 25px;
  outline: none;
  transition: all 0.3s ease;

  &:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
  }

  &::placeholder {
    color: #999;
  }
}

.search-button {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #666;
  transition: color 0.3s ease;

  &:hover {
    color: #28a745;
  }

  svg {
    vertical-align: middle;
  }
}