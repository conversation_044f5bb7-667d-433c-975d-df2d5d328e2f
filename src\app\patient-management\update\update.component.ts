import { Component } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { environment } from '../../../environments/environment';
import { RouterLink } from '@angular/router';

interface PatientData {
  id: string | null;
  firstName: string;
  lastName: string;
  age: number | null;
  dob: string;
  nic: string;
  contactNo: string;
  email: string;
  address: string;
  bloodGroup: string;
  gender: string;
  patientStatus: boolean;
  admittedDateTime: string;
  allergyStatus: boolean;
  remarks: string;
  guardianNIC: string;
  guardianName: string;
  guardianContact: string;
}

@Component({
  selector: 'app-update',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink],
  templateUrl: './update.component.html',
  styleUrl: './update.component.css'
})
export class UpdateComponent {
  private API_URL = environment.PATIENT_MANAGEMENT_BASE_URL;
  showSuccessModal = false;
  showErrorModal = false;
  errorMessage = '';
  
  patient: PatientData = {
    id: null,
    firstName: '',
    lastName: '',
    age: null,
    dob: '',
    nic: '',
    contactNo: '',
    email: '',
    address: '',
    bloodGroup: '',
    gender: '',
    patientStatus: false,
    admittedDateTime: '',
    allergyStatus: false,
    remarks: '',
    guardianNIC: '',
    guardianName: '',
    guardianContact: ''
  };

  searchTerm: string = '';
  searchType: string = 'name';
  searchResults: any[] = [];
  errors: { [key: string]: string } = {};
  bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

  constructor(private http: HttpClient) {}

  search() {
    if (!this.searchTerm.trim()) {
      this.errorMessage = 'Please enter a search term';
      this.showErrorModal = true;
      return;
    }

    let searchUrl = '';
    switch (this.searchType) {
      case 'name':
        searchUrl = `${this.API_URL}/patient-search-by-name/${this.searchTerm}`;
        break;
      case 'nic':
        searchUrl = `${this.API_URL}/patient-search-by-nic/${this.searchTerm}`;
        break;
      case 'id':
        searchUrl = `${this.API_URL}/patient-search-by-id/${this.searchTerm}`;
        break;
    }

    this.http.get<any>(searchUrl).subscribe({
      next: (data) => {
        if (data) {
          this.searchResults = Array.isArray(data) ? data : [data];
        } else {
          this.searchResults = [];
          this.errorMessage = 'No patient found';
          this.showErrorModal = true;
        }
      },
      error: (error) => {
        console.error('Error:', error);
        this.errorMessage = 'Error searching for patient';
        this.showErrorModal = true;
      }
    });
  }

  selectPatient(patient: any) {
    this.patient = {
      ...patient,
      dob: patient.dob ? new Date(patient.dob).toISOString().split('T')[0] : '',
      patientStatus: patient.patientStatus === 'true',
      allergyStatus: patient.allergyStatus === 'true'
    };
    this.searchResults = [];
  }

  validateForm(): boolean {
    this.errors = {};
    let isValid = true;

    // Required fields validation
    if (!this.patient.firstName?.trim()) {
      this.errors['firstName'] = 'First name is required';
      isValid = false;
    }

    if (!this.patient.lastName?.trim()) {
      this.errors['lastName'] = 'Last name is required';
      isValid = false;
    }

    if (!this.patient.age || this.patient.age <= 0) {
      this.errors['age'] = 'Valid age is required';
      isValid = false;
    }

    if (!this.patient.dob) {
      this.errors['dob'] = 'Date of birth is required';
      isValid = false;
    }

    if (!this.patient.nic?.trim()) {
      this.errors['nic'] = 'NIC is required';
      isValid = false;
    } else if (!this.validateNIC(this.patient.nic)) {
      this.errors['nic'] = 'Invalid NIC format';
      isValid = false;
    }

    if (!this.patient.contactNo?.trim()) {
      this.errors['contactNo'] = 'Contact number is required';
      isValid = false;
    } else if (!this.validatePhone(this.patient.contactNo)) {
      this.errors['contactNo'] = 'Invalid contact number format';
      isValid = false;
    }

    if (this.patient.email && !this.validateEmail(this.patient.email)) {
      this.errors['email'] = 'Invalid email format';
      isValid = false;
    }

    if (!this.patient.gender) {
      this.errors['gender'] = 'Gender is required';
      isValid = false;
    }

    if (!this.patient.address?.trim()) {
      this.errors['address'] = 'Address is required';
      isValid = false;
    } else if (this.patient.address.trim().length < 3) {
      this.errors['address'] = 'Address must be at least 10 characters long';
      isValid = false;
    }

    if (!this.patient.bloodGroup) {
      this.errors['bloodGroup'] = 'Blood group is required';
      isValid = false;
    }

    // Guardian validation
    if (this.patient.guardianNIC?.trim() || this.patient.guardianName?.trim() || this.patient.guardianContact?.trim()) {
      if (!this.patient.guardianNIC?.trim()) {
        this.errors['guardianNIC'] = 'Guardian NIC is required';
        isValid = false;
      } else if (!this.validateNIC(this.patient.guardianNIC)) {
        this.errors['guardianNIC'] = 'Invalid Guardian NIC format';
        isValid = false;
      }

      if (!this.patient.guardianName?.trim()) {
        this.errors['guardianName'] = 'Guardian name is required';
        isValid = false;
      }

      if (!this.patient.guardianContact?.trim()) {
        this.errors['guardianContact'] = 'Guardian contact is required';
        isValid = false;
      } else if (!this.validatePhone(this.patient.guardianContact)) {
        this.errors['guardianContact'] = 'Invalid guardian contact format';
        isValid = false;
      }
    }

    return isValid;
  }

  validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    return emailRegex.test(email);
  }

  validatePhone(phone: string): boolean {
    const phoneRegex = /^[0-9]{10}$/;
    return phoneRegex.test(phone);
  }

  validateNIC(nic: string): boolean {
    const oldNICRegex = /^[0-9]{9}[VvXx]$/;
    const newNICRegex = /^[0-9]{12}$/;
    return oldNICRegex.test(nic) || newNICRegex.test(nic);
  }

  saveCustomer() {
    if (!this.validateForm()) {
      this.errorMessage = 'Please fix the validation errors before saving.';
      this.showErrorModal = true;
      return;
    }

    const patientToSave = {
      ...this.patient,
      patientStatus: this.patient.patientStatus.toString(),
      allergyStatus: this.patient.allergyStatus.toString()
    };

    const headers = new HttpHeaders().set('Content-Type', 'application/json');
    
    this.http.put(`${this.API_URL}/update-patient`, patientToSave, { headers, responseType: 'text' })
      .subscribe({
        next: () => {
          this.showSuccessModal = true;
          this.searchTerm = '';
          this.searchResults = [];
        },
        error: (error: HttpErrorResponse) => {
          this.errorMessage = 'Failed to update patient. Please try again later.';
          if (error.error && typeof error.error === 'string') {
            this.errorMessage = error.error;
          }
          this.showErrorModal = true;
        }
      });
  }

  closeSuccessModal() {
    this.showSuccessModal = false;
    this.resetForm();
  }

  closeErrorModal() {
    this.showErrorModal = false;
    this.errorMessage = '';
  }

  resetForm() {
    this.patient = {
      id: null,
      firstName: '',
      lastName: '',
      age: null,
      dob: '',
      nic: '',
      contactNo: '',
      email: '',
      address: '',
      bloodGroup: '',
      gender: '',
      patientStatus: false,
      admittedDateTime: '',
      allergyStatus: false,
      remarks: '',
      guardianNIC: '',
      guardianName: '',
      guardianContact: ''
    };
    this.errors = {};
  }

  hasError(field: string): boolean {
    return this.errors.hasOwnProperty(field);
  }

  getError(field: string): string {
    return this.errors[field];
  }
}