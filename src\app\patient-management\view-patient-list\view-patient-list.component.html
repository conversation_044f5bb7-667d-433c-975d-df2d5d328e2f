<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Patient List</p>
    </div>
    <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ View Patient
        </p>
      </div>
  </div>

  <!-- Search Section -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg p-4">
        <div class="row g-3">
          <div class="col-md-3">
            <select class="form-control bg-light">
              <option value="name">Search by Name</option>
              <option value="nic">Search by NIC</option>
              <option value="contact">Search by Contact</option>
              <option value="guardian">Search by Guardian</option>
            </select>
          </div>
          <div class="col-md-7">
            <div class="search-bar">
              <input type="text" 
                     class="form-control search-input" 
                     >
              <button class="search-btn">
                <i class="bi bi-search search-icon"></i>
              </button>
            </div>
          </div>
          <div class="col-md-2">
            <button class="btn btn-success w-100">
              Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Patient List Table -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th class="text-custom-green">Count</th>
                  <th class="text-custom-green">Patient ID</th>
                  <th class="text-custom-green">Name</th>
                  <th class="text-custom-green">NIC</th>
                  <th class="text-custom-green">Age</th>
                  <th class="text-custom-green">Gender</th>
                  <th class="text-custom-green">Contact</th>
                  <th class="text-custom-green">Address</th>
                  <th class="text-custom-green">Guardian</th>
                  <th class="text-custom-green">Guardian Contact</th>
                  <th class="text-custom-green">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let patient of patientList; index as i">
                  <td>{{i+1}}</td>
                  <td>P000{{patient.id}}</td>
                  <td>{{ patient.firstName }} {{patient.lastName}}</td>
                  <td>{{ patient.nic }}</td>
                  <td>{{ patient.age }}</td>
                  <td>{{ patient.gender }}</td>
                  <td>{{ patient.contactNo}}</td>
                  <td>{{ patient.address }}</td>
                  <td>{{ patient.guardianName }}</td>
                  <td>{{ patient.guardianContact }}</td>
                  <td>
                    <button class="btn btn-outline-success btn-sm" 
                            data-bs-toggle="modal" 
                            [attr.data-bs-target]="'#viewModal' + patient.id">
                      <i class="bi bi-eye"></i> View
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Patient Details Modal -->
  <div *ngFor="let patient of patientList" 
       class="modal fade" 
       [id]="'viewModal' + patient.id" 
       tabindex="-1" 
       aria-labelledby="viewModalLabel" 
       aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title text-custom-dark">Patient Details</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Personal Information</h3>
            <div class="row g-3">
              <div class="col-md-6">
                <p><span class="text-label">Name:</span> 
                   <span class="text-value">{{patient.firstName}} {{patient.lastName}}</span></p>
                <p><span class="text-label">NIC:</span>
                   <span class="text-value">{{patient.nic}}</span></p>
                <p><span class="text-label">Age:</span>
                   <span class="text-value">{{patient.age}}</span></p>
                <p><span class="text-label">Gender:</span>
                   <span class="text-value">{{patient.gender}}</span></p>
                <p><span class="text-label">Contact:</span>
                   <span class="text-value">{{patient.contactNo}}</span></p>
              </div>
              <div class="col-md-6">
                <p><span class="text-label">Address:</span>
                   <span class="text-value">{{patient.address}}</span></p>
                <p><span class="text-label">Blood Group:</span>
                   <span class="text-value">{{patient.bloodGroup}}</span></p>
                <p><span class="text-label">Guardian Name:</span>
                   <span class="text-value">{{patient.guardianName}}</span></p>
                <p><span class="text-label">Guardian Contact:</span>
                   <span class="text-value">{{patient.guardianContact}}</span></p>
                <p><span class="text-label">Guardian NIC:</span>
                   <span class="text-value">{{patient.guardianNIC}}</span></p>
              </div>
            </div>
          </div>

          <div class="section-container">
            <h3 class="text-custom-dark mb-3">Medical Information</h3>
            <div class="row g-3">
              <div class="col-12">
                <p><span class="text-label">Patient Status:</span>
                   <span class="badge" [ngClass]="patient.patientStatus === 'true' ? 'bg-success' : 'bg-secondary'">
                     {{patient.patientStatus === 'true' ? 'Admitted' : 'Not Admitted'}}
                   </span>
                </p>
                <p><span class="text-label">Allergy Status:</span>
                   <span class="badge" [ngClass]="patient.allergyStatus === 'true' ? 'bg-danger' : 'bg-success'">
                     {{patient.allergyStatus === 'true' ? 'Has Allergies' : 'No Allergies'}}
                   </span>
                </p>
                <p><span class="text-label">Remarks:</span>
                   <span class="text-value">{{patient.remarks}}</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>