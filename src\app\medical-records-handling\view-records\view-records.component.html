<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">View Records</p>
    </div>
    <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ View Records
        </p>
      </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <div class="p-4">
          <!-- Search Filters -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Search Filters</h3>
            <div class="row g-3">
              <div class="col-md-6">
                <input type="text" class="form-control bg-light" [(ngModel)]="searchText" 
                       placeholder="Enter Record ID">
              </div>
              <div class="col-md-4">
                <input type="date" class="form-control bg-light" [(ngModel)]="searchDate">
              </div>
              <div class="col-md-2">
                <button class="btn btn-success w-100" (click)="searchRecord()">Search</button>
              </div>
            </div>
          </div>

          <!-- Records List -->
          <div class="section-container">
            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th>Record ID</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let record of records">
                    <td>{{ record.recordId }}</td>
                    <td>{{ record.recordDate }}</td>
                    <td>
                      <button class="btn btn-sm btn-outline-primary me-2" (click)="openModal(record)">View</button>
                      <button class="btn btn-sm btn-outline-success me-2" (click)="updateRecord(record)">Update</button>
                      <button class="btn btn-sm btn-outline-danger" (click)="deleteRecord(record)">Delete</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Record Details Modal -->
<div class="modal-overlay" *ngIf="isModalOpen">
  <div class="modal-container">
    <div class="alert-box">
      <div class="alert-content">
        <h2>Record Details</h2>
        <div class="alert-details">
          <div class="detail-row">
            <label><strong>Record ID:</strong></label>
            <span>{{selectedRecord?.recordId}}</span>
          </div>
          <div class="detail-row">
            <label><strong>Patient ID:</strong></label>
            <span>{{selectedRecord?.patientID}}</span>
          </div>
          <div class="detail-row">
            <label><strong>Date:</strong></label>
            <span>{{selectedRecord?.recordDate}}</span>
          </div>
          <div class="detail-row">
            <label><strong>Description:</strong></label>
            <span>{{selectedRecord?.description}}</span>
          </div>
        </div>
      </div>
      <div class="alert-button">
        <button class="btn btn-secondary me-2" (click)="closeModal()">Close</button>
      </div>
    </div>
  </div>
</div>