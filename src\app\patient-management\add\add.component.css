.container {
  font-family: 'Poppins', sans-serif;
  padding: 20px;
}

.page-title {
  font-size: 2rem;
  color: #38823E;
  font-weight: 600;
}

.breadcrumb {
  font-size: medium;
  color: #6c757d;
  margin-top: 1%;
  text-align: right;
}

.card {
  border-radius: 0.4rem;
  background-color: #fff;
}

.custom-shadow {
  box-shadow: 8px 8px 8px rgba(11, 105, 30, 0.2);
}

.section-container {
  background-color: #dcf4da;
  padding: 20px;
  border-radius: 0.4rem;
  margin-bottom: 20px;
}

.text-custom-dark {
  color: #0c5e07;
  font-size: 1.2rem;
  font-weight: bold;
}

.text-custom-green {
  color: #38823E;
  font-weight: 500;
}

.form-control {
  border-color: #80CD84;
  font-size: 0.9rem;
}

.form-control::placeholder {
  color: #80CD84;
}

.form-control:focus {
  border-color: #38823E;
  box-shadow: 0 0 0 0.2rem rgba(56, 130, 62, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
  display: block;
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-check-input:checked {
  background-color: #38823E;
  border-color: #38823E;
}

.btn-success {
  background-color: #38823E;
  border-color: #38823E;
  font-weight: 500;
  padding: 8px 25px;
}

.btn-success:hover {
  background-color: #0c5e07;
  border-color: #0c5e07;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  font-weight: 500;
  padding: 8px 25px;
}

.btn-danger:hover {
  background-color: #bb2d3b;
  border-color: #bb2d3b;
}

textarea {
  resize: none;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.alert-box {
  text-align: center;
}

.alert-icon {
  width: 60px;
  height: 60px;
  background-color: #38823E;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
}

.alert-icon.error {
  background-color: #dc3545;
}

.alert-icon span {
  color: white;
  font-size: 30px;
}

.alert-content h2 {
  color: #38823E;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.alert-content h2.text-danger {
  color: #dc3545;
}

.alert-details {
  text-align: left;
  margin: 20px 0;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.detail-row {
  display: flex;
  margin: 10px 0;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row label {
  width: 120px;
  color: #666;
}

.detail-row span {
  flex: 1;
  color: #333;
}

.alert-button {
  margin-top: 20px;
}

.alert-button .btn {
  min-width: 100px;
  padding: 8px 25px;
}

@media (max-width: 768px) {
  .page-title {
      font-size: 1.5rem;
  }
  
  .section-container {
      padding: 15px;
  }
}