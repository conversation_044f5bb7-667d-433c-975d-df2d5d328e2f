import { CommonModule, <PERSON><PERSON><PERSON>, Ng<PERSON>f } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { catchError, throwError } from 'rxjs';
import { HeaderAdminSectionComponent } from "../common/header-admin-section/header-admin-section.component";
import { environment } from '../../../environments/environment';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-view-category',
  standalone: true,
  imports: [CommonModule, FormsModule, NgFor, NgIf, HeaderAdminSectionComponent, RouterLink],
  templateUrl: './view-category.component.html',
  styleUrl: './view-category.component.css'
})
export class ViewCategoryComponent {
  public searchText : String = "";
  public categoryList : any = null;

  constructor(private http:HttpClient) {
    this.loadCategoryList();
  }

  loadCategoryList() : void {
    this.http.get(`${environment.CATEGORY_BASE_URL}/all`).pipe(
      catchError(error => {
        this.categoryList = null;
        return throwError(() => new Error('Error fetching data.'));
      })
    ).subscribe(data=>{
      if (this.isEmpty(data)) {
        this.categoryList = null;
      } else {
        this.categoryList = data;
      }
    });
  }

  deleteCategory(category : any) : void {
    const id = parseInt(category.categoryId);
    if (window.confirm("Do you want to delete?")) {
      this.http.delete(`${environment.CATEGORY_BASE_URL}/delete/${id}`, { responseType: 'text' }).pipe(
        catchError(error => {
          alert("Error.")
          return throwError(() => new Error('Error fetching data.'));
        })
      ).subscribe(data=>{
        this.loadCategoryList();
        alert(data);
      });
    }
  }

  get filteredData() : any {
    return this.categoryList.filter((category : any) =>
      Object.values(category).some((value : any) =>
        value.toString().toLowerCase().startsWith(this.searchText.toLowerCase())
      )
    );
  }
    
  isEmpty(array : any) : boolean {
    return !array || array.length == 0;
  }
}
