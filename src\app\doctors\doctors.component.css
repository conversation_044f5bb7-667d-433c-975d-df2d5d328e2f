.profile-image {
    width: 150px;  
    height: 150px; 
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid #ddd;
    margin: 0 auto; 
}
.vet-card {
    width: 300px;
    background-color: #fff;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
    position: relative;
    border: 1px solid #e6ffe8;
}

.vet-card:hover {
    transform: translateY(-5px);
}

.vet-avatar {
    text-align: center;
    margin-bottom: 15px;
}

.vet-info {
    text-align: center;
}

.vet-info h3 {
    color: #000;
    font-size: 1.25rem;
    margin-bottom: 10px;
}

.vet-info p {
    margin: 5px 0;
    color: #666;
}

.vet-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.btn {
    padding: 8px 16px;
    border-radius: 8px;
}

.btn-success {
    background-color: #4eaf5a;
    border: none;
}

.btn-danger {
    background-color: #d32727;
    border: none;
}
.modal-content {
    border-radius: 15px;
}
.modal-header {
    background-color: #4eaf5a;
    color: white;
    border-radius: 15px 15px 0 0;
}
.form-control {
    border: 1px solid #e6ffe8;
    border-radius: 10px;
    background-color: #e6ffe8;
}
   .container-fluid {
    padding: 0;
}

#prescription-name {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    color: #38823E;
    font-weight: 600;
    text-align: center;
}

.slider {
    background: transparent;
    border: none;
    cursor: pointer;
    transition: transform 0.1s ease-in-out;
    margin: 10%;
    font-size: x-large;
}

.slider:hover {
    transform: scale(1.2);
}

#home-prescription-name {
    font-size: medium;
    color: #6c757d;
    margin-top: 1%;
    text-align: right;
}

.search-bar {
    position: sticky;
    width: 100%;
    height: auto;
    margin-top: 1%;
    background:transparent;
    border-width: 1px;
    border: #0c5e07;
}

.search-input {
    border-radius: 20px;
    padding-right: 40px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    font-size: 16px;
}

.search-btn {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border: none;
    background: transparent;
    padding: 10px 15px;
    cursor: pointer;
    margin-right: 5px;
}

.search-icon {
    font-size: 18px;
    color: #38823E;
    transition: color 0.3s;
    border-radius: 20px;
    background: transparent;
}
