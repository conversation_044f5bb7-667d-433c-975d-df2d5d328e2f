<div class="alerts-container">
  <app-alert *ngFor="let alert of alerts" [alert]="alert"></app-alert>
</div>

<div class="container-fluid mt-2">
  <div class="row col-12">
    <div class="col-4" id="prescription-name">
      <p>Prescription</p>
    </div>

    <div class="col-5" id="Search">
      <div class="search-bar">
        <input
          #searchInput
          type="text"
          class="form-control search-input"
          [placeholder]="searchPlaceholder"
          aria-label="Search"
          (keyup.enter)="searchByID(searchInput.value)"
        />

        <button class="search-btn" (click)="searchByID(searchInput.value)">
          <i class="bi bi-search search-icon"></i>
        </button>
      </div>
    </div>

    <div class="col-1">
      <button class="slider">
        <i
          class="bi bi-sliders"
          data-bs-toggle="modal"
          data-bs-target="#exampleModal"
          data-bs-whatever="@mdo"
        ></i>
      </button>
    </div>
    <div class="col col-2" id="home-prescription-name">     
    <p class="breadcrumb">
      <a routerLink="/" class="text-decoration-none">Home</a>/ Prescription
    </p>
    </div>
  </div>
</div>

<!----------------- date range selctor ----------------->

<div
  class="modal fade"
  id="exampleModal"
  tabindex="-1"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="exampleModalLabel">
          Select Your Date Range
        </h1>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col mb-3">
              <label for="start-date" class="col-form-label">Start Date:</label>
              <input
                type="date"
                class="form-control"
                id="start-date"
                [(ngModel)]="startDate"
                name="startDate"
              />
            </div>
            <div class="col mb-3">
              <label for="end-date" class="col-form-label">End Date:</label>
              <input
                type="date"
                class="form-control"
                id="end-date"
                [(ngModel)]="endDate"
                name="endDate"
              />
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Close
        </button>
        <button type="button" class="btn btn-success" (click)="searchByDate()">
          Search
        </button>
      </div>
    </div>
  </div>
</div>

