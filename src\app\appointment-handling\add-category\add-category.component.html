<div class="container">
    <!-- Header Section -->
    <div class="row">
      <div class="col-4">
        <p class="page-title">Add Category</p>
      </div>
      <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/Add Category
        </p>
      </div>
    </div>
  
    <!-- Main Content -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card border-0 custom-shadow shadow-lg">
          <form class="p-4">
            <!-- Category Information -->
            <div class="section-container">
              <h3 class="text-custom-dark mb-3">Category Information</h3>
              <div class="row g-3">
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" placeholder="Category ID">
                </div>
                <div class="col-12">
                  <textarea class="form-control bg-light" [(ngModel)]="category.description" 
                            name="description" placeholder="Category Description" rows="3"></textarea>
                </div>
              </div>
            </div>
  
            <!-- Submit Button -->
            <div class="d-flex justify-content-end mt-4">
              <button type="button" class="btn btn-success px-4" 
                      (click)="addCategory(); category.description = '';">Add Category</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>