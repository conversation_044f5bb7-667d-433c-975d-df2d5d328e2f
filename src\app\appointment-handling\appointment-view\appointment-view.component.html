<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">View Appointments</p>
    </div>
    <div class="col-6"></div>
    <div class="col-2">
      <p class="breadcrumb">
        <a routerLink="/" class="text-decoration-none">Home</a>/ View
        Appointments
      </p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0">
        <div class="p-4">
          <!-- Search Section -->
          <div class="section-container shadow-lg mb-4">
            <div class="row g-3">
              <div class="col-md-6">
                <input
                  type="text"
                  class="form-control bg-light"
                  placeholder="Search appointments"
                />
              </div>
              <div class="col-md-4">
                <input type="date" class="form-control bg-light" />
              </div>
              <div class="col-md-2">
                <button class="btn btn-success w-100">
                  Reset
                </button>
              </div>
            </div>
          </div>

          <!-- Appointment List Table -->
          <div class="row mt-4">
            <div class="col-12">
              <div class="card border-0 custom-shadow shadow-lg">
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th class="text-custom-green">ID</th>
                          <th class="text-custom-green">Description</th>
                          <th class="text-custom-green">Date</th>
                          <th class="text-custom-green">Time</th>
                          <th class="text-custom-green">Patient ID</th>
                          <th class="text-custom-green">Category ID</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let appointment of appointmentList">
                          <td>{{ appointment.appointmentId }}</td>
                          <td>{{ appointment.description }}</td>
                          <td>{{ appointment.date }}</td>
                          <td>{{ appointment.time }}</td>
                          <td>{{ appointment.patientId }}</td>
                          <td>{{ appointment.categoryId }}</td>
                        </tr>
                        <tr *ngIf="appointmentList.length === 0">
                          <td colspan="6" class="text-center">
                            No appointments found.
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
