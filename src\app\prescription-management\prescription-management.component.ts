import { Component } from '@angular/core';
import { PatientInfoComponent } from './component/patient-info/patient-info.component';
import { PrescriptionDataEntryComponent } from './component/prescription-data-entry/prescription-data-entry.component';
import { ReportViewComponent } from './component/report-view/report-view.component';
import { SearchComponent } from './component/search/search.component';


@Component({
  selector: 'app-prescription-management',
  standalone: true,
  imports: [PatientInfoComponent, PrescriptionDataEntryComponent, ReportViewComponent, SearchComponent],
  templateUrl: './prescription-management.component.html',
  styleUrl: './prescription-management.component.css'
})
export class PrescriptionManagementComponent {

 
}
