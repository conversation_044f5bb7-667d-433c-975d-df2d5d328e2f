import { Component } from '@angular/core';

import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-search',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './search.component.html',
  styleUrl: './search.component.css'
})
export class SearchComponent {
  public patient: any = {
    id: null,
    firstName: '',
    lastName: '',
    age: null,
    dob: '',
    nic: '',
    contactNo: '',
    email: '',
    address: '',
    bloodGroup: '',
    gender: '',
    patientStatus: false,
    admittedDateTime: '',
    allergyStatus: false,
    remarks: '',
    guardianNIC: '',
    guardianName: '',
    guardianContact: ''
  };

  public searchTerm: string = '';
  public searchType: string = 'name';
  public searchResults: any[] = [];

  private API_URL = environment.PATIENT_MANAGEMENT_BASE_URL

  constructor(private http: HttpClient) {}

  search() {
    if (!this.searchTerm) {
      alert('Please enter a search term');
      return;
    }

    let searchUrl = '';
    switch (this.searchType) {
      case 'name':
        searchUrl = `${this.API_URL}/patient-search-by-name/${this.searchTerm}`;
        break;
      case 'nic':
        searchUrl = `${this.API_URL}/patient-search-by-nic/${this.searchTerm}`;
        break;
      case 'id':
        searchUrl = `${this.API_URL}/patient-search-by-id/${this.searchTerm}`;
        break;
    }

    this.http.get<any>(searchUrl).subscribe({
      next: (data) => {
        if (data) {
          // If the response is an array, set it as search results
          if (Array.isArray(data)) {
            this.searchResults = data;
          } else {
            // If single result, add it to results array
            this.searchResults = [data];
          }
        } else {
          this.searchResults = [];
          alert('No patient found');
        }
      },
      error: (error) => {
        console.error('Error:', error);
        alert('Error searching for patient');
      }
    });
  }

  selectPatient(patient: any) {
    this.patient = {
      ...patient,
      dob: patient.dob ? new Date(patient.dob).toISOString().split('T')[0] : '',
      patientStatus: patient.patientStatus === 'true',
      allergyStatus: patient.allergyStatus === 'true'
    };
    this.searchResults = []; // Clear search results after selection
  }
}
