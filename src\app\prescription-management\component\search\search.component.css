@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');
.alerts-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 1000; 
  }

.container-fluid {
    padding: 0;
}

#prescription-name {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    color: #38823E;
    font-weight: 600;
    text-align: center;
}

.slider {
    background: transparent;
    border: none;
    cursor: pointer;
    transition: transform 0.1s ease-in-out;
    margin: 10%;
    font-size: x-large;
}

.slider:hover {
    transform: scale(1.2);
}

#home-prescription-name {
    font-size: medium;
    color: #6c757d;
    margin-top: 1%;
    text-align: right;
}

.search-bar {
    position: sticky;
    width: 100%;
    height: auto;
    margin-top: 1%;
    background:transparent;
    border-width: 1px;
    border: #0c5e07;
}

.search-input {
    border-radius: 20px;
    padding-right: 40px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    font-size: 16px;
}

.search-btn {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border: none;
    background: transparent;
    padding: 10px 15px;
    cursor: pointer;
    margin-right: 5px;
}

.search-icon {
    font-size: 18px;
    color: #38823E;
    transition: color 0.3s;
    border-radius: 20px;
    background: transparent;
}

.form-control{
    font-size: small;
    border-color: #80CD84;
}

.form-control::placeholder {
    color: #80CD84;
    text-align: left;
    text-justify: auto;
}