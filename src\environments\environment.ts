interface Environment {
    APPOINTMENT_MANAGEMENT_BASE_URL: string;
    CATEGORY_BASE_URL: string;
    PATIENT_MANAGEMENT_BASE_URL: string;
    PRESCRIPTION_MANAGEMENT_BASE_URL: string;
    RECORD_MANAGEMENT_BASE_URL: string;
}

export const environment: Environment = {
    APPOINTMENT_MANAGEMENT_BASE_URL: 'http://localhost:8080/appointment',
    CATEGORY_BASE_URL: 'http://localhost:8080/category',
    PATIENT_MANAGEMENT_BASE_URL: 'http://localhost:8081/patient',
    PRESCRIPTION_MANAGEMENT_BASE_URL: 'http://localhost:8082/api/v1/prescription',
    RECORD_MANAGEMENT_BASE_URL: 'http://localhost:8083/record'
};
