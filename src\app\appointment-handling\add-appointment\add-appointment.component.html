<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Add Appointment</p>
    </div>
    <div class="col-6"></div>
    <div class="col-2">
      <p class="breadcrumb">
        <a routerLink="/" class="text-decoration-none">Home</a>/ Add Appointment
      </p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <!-- Patient Search Section -->
        <div class="section-container mb-4">
          <h3 class="text-custom-dark mb-3">Patient Search</h3>
          <div class="row g-3">
            <div class="col-md-4">
              <input type="text" class="form-control bg-light" [(ngModel)]="patientId" id="txtPatientId" placeholder="Search By Patient Id" (keyup.enter)="fetchPatientDetails()">
            </div>
          </div>
        </div>

        <!-- Patient Information -->
        <form #appointmentForm="ngForm" (ngSubmit)="onSubmit()" class="p-4">
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Patient Information</h3>
            <div class="row g-3">
              <div class="col-md-6">
                <input type="text" class="form-control bg-light" [(ngModel)]="firstName" name="firstName" placeholder="Enter Name">
              </div>
              <div class="col-md-6">
                <input type="text" class="form-control bg-light" [(ngModel)]="nic" name="nic" placeholder="NIC">
              </div>
              <div class="col-md-6">
                <input type="tel" class="form-control bg-light" [(ngModel)]="contactNo" name="contactNo" placeholder="Mobile Number">
              </div>
              <div class="col-md-6">
                <input type="email" class="form-control bg-light" [(ngModel)]="email" name="email" placeholder="Email Address">
              </div>
              <!-- <div class="col-md-4">
                <div class="btn-group w-100" role="group" aria-label="Gender">
                  <button type="button" class="btn" [ngClass]="{'active-btn': isMaleSelected(), 'inactive-btn': !isMaleSelected()}" (click)="selectGender('male')">Male</button>
                  <button type="button" class="btn" [ngClass]="{'active-btn': isFemaleSelected(), 'inactive-btn': !isFemaleSelected()}" (click)="selectGender('female')">Female</button>
                </div>
              </div> -->
            </div>
          </div>

          <!-- Appointment Details -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Appointment Details</h3>
            <div class="row g-3">
              <div class="col-md-4">
                <input type="date" class="form-control bg-light" [(ngModel)]="date" name="date">
              </div>
              <div class="col-md-4">
                <input type="time" class="form-control bg-light" [(ngModel)]="time" name="time">
              </div>
              <div class="col-md-4">
                <select class="form-control bg-light" [(ngModel)]="categoryId" name="category">
                  <option value="">Select Category</option>
                  <option *ngFor="let category of categoryList" [value]="category.categoryId">
                    {{category.description}}
                  </option>
                </select>
              </div>
              <div class="col-12">
                <textarea class="form-control bg-light" [(ngModel)]="description" name="description" placeholder="Description" rows="3"></textarea>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="d-flex justify-content-end mt-4">
            <button type="submit" class="btn btn-success px-4">Add Appointment</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal -->
<div class="modal-overlay" *ngIf="showModal">
  <div class="modal-container">
    <div class="modal-content">
      <div class="alert-box">
        <div class="alert-icon">
          <span>✓</span>
        </div>
        <div class="alert-content">
          <h2>Appointment successfully placed!</h2>
          <div class="alert-details">
            <div class="detail-row">
              <label><strong>Date:</strong></label>
              <span>{{formData.date}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Name:</strong></label>
              <span>{{formData.name}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Contact No:</strong></label>
              <span>{{formData.mobile}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Category:</strong></label>
              <span>{{formData.category}}</span>
            </div>
          </div>
        </div>
        <div class="alert-button">
          <button class="btn btn-success" (click)="closeAlert()">OK</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" *ngIf="isLoading">
  <div class="spinner"></div>
  <p>...Adding appointment...</p>
</div>

