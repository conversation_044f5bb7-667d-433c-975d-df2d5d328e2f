import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NavBarComponent } from '../../common/nav-bar/nav-bar.component';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { HeaderAdminSectionComponent } from "../common/header-admin-section/header-admin-section.component";
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-delete-appointment',
  standalone: true,
  imports: [FormsModule, CommonModule, HeaderAdminSectionComponent, RouterLink],
  templateUrl: './delete-appointment.component.html',
  styleUrls: ['./delete-appointment.component.css']
})
export class DeleteAppointmentComponent implements OnInit {
  showModal = false;
  showModalVerify = false;
  searchQuery: string = '';
  selectedAppointment: any = {};
  patientDetails: any = null;
  showNoAppointmentMessage: boolean = false;

  constructor(private http: HttpClient) {}

  ngOnInit() {
    
  }

  appointment = {
    appointmentId: '',
    name: '',
    NIC: '',
    mobileNumber: '',
    emailAddress: '',
    gender: 'male',
    date: '',
    time: '',
    description: '',
    category: ''
  };

  onSearchInput() {
    if (this.searchQuery.trim() === '') {
      this.selectedAppointment = {};
      this.showNoAppointmentMessage = false;
      return;
    }

    console.log('Searching for:', this.searchQuery);

    // First try to search by appointment ID
    this.http.get<any>(`${environment.APPOINTMENT_MANAGEMENT_BASE_URL}/search-by-appointmentId/${this.searchQuery}`).subscribe({
      next: (res) => {
        console.log('Appointment search response:', res);
        if (res && res.length > 0) {
          console.log('Found appointment:', res[0]);
          this.selectedAppointment = {
            ...res[0],
            name: '', // Will be updated from patient details
            NIC: ''  // Will be updated from patient details
          };
          this.showNoAppointmentMessage = false;

          // Get patient details
          this.http.get<any>(`${environment.PATIENT_MANAGEMENT_BASE_URL}/patient-search-by-id/${this.selectedAppointment.patientId}`).subscribe({
            next: (patientRes) => {
              console.log('Patient details:', patientRes);
              if (patientRes) {
                this.patientDetails = patientRes;
                // Update appointment with patient details
                this.selectedAppointment = {
                  ...this.selectedAppointment,
                  name: patientRes.firstName + ' ' + patientRes.lastName,
                  NIC: patientRes.nic,
                  mobileNumber: patientRes.contactNo,
                  emailAddress: patientRes.email,
                  gender: patientRes.gender
                };
              }
            },
            error: (err) => {
              console.error('Error fetching patient details:', err);
              this.showNoAppointmentMessage = true;
            }
          });
        } else {
          // Try searching by patient ID/NIC
          console.log('No appointment found, trying patient search');
          this.http.get<any>(`${environment.PATIENT_MANAGEMENT_BASE_URL}/patient-search-by-id/${this.searchQuery}`).subscribe({
            next: (patientRes) => {
              console.log('Patient search response:', patientRes);
              if (patientRes) {
                this.patientDetails = patientRes;
                this.http.get<any>(`${environment.APPOINTMENT_MANAGEMENT_BASE_URL}/search-by-patientId/${patientRes.id}`).subscribe({
                  next: (appointmentRes) => {
                    console.log('Appointments for patient:', appointmentRes);
                    if (appointmentRes && appointmentRes.length > 0) {
                      this.selectedAppointment = {
                        ...appointmentRes[0],
                        name: patientRes.firstName + ' ' + patientRes.lastName,
                        NIC: patientRes.nic,
                        mobileNumber: patientRes.contactNo,
                        emailAddress: patientRes.email,
                        gender: patientRes.gender
                      };
                      this.showNoAppointmentMessage = false;
                    } else {
                      this.selectedAppointment = {};
                      this.showNoAppointmentMessage = true;
                    }
                  },
                  error: (err) => {
                    console.error('Error fetching appointment:', err);
                    this.showNoAppointmentMessage = true;
                  }
                });
              } else {
                this.selectedAppointment = {};
                this.showNoAppointmentMessage = true;
              }
            },
            error: (err) => {
              console.error('Error fetching patient:', err);
              this.showNoAppointmentMessage = true;
            }
          });
        }
      },
      error: (err) => {
        console.error('Error searching appointment:', err);
        this.showNoAppointmentMessage = true;
      }
    });
  }

  delete() {
    if (this.selectedAppointment && this.selectedAppointment.appointmentId) {
      const appointmentId = this.selectedAppointment.appointmentId;
      console.log('Deleting appointment:', appointmentId);
      
      this.http.delete(`${environment.APPOINTMENT_MANAGEMENT_BASE_URL}/delete/${appointmentId}`).subscribe({
        next: () => {
          console.log('Successfully deleted appointment:', appointmentId);
          this.resetForm();
          this.closeAll();
        },
        error: (err) => {
          console.error('Error deleting appointment:', err);
        }
      });
    } else {
      console.log("No appointment selected for deletion.");
    }
  }

  resetForm(): void {
    this.appointment = {
      appointmentId: '',
      name: '',
      NIC: '',
      mobileNumber: '',
      emailAddress: '',
      gender: 'male',
      date: '',
      time: '',
      description: '',
      category: ''
    };
    this.searchQuery = '';
    this.selectedAppointment = {};
    this.patientDetails = null;
    this.showNoAppointmentMessage = false;
  }

  public closeAlert() {
    this.showModalVerify = true;
  }
  
  public cancel() {
    this.showModal = false;
  }
  
  public onSubmit() {
    this.showModal = true;
  }
  public closeAll(){
    this.showModalVerify = false;
    this.showModal = false;
  }
}
