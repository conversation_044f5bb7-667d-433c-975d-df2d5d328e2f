<div class="container">
    <!-- Header Section -->
    <div class="row">
      <div class="col-4">
        <p class="page-title">View Reports</p>
      </div>
      <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ View Reports
        </p>
      </div>
    </div>
  
    <!-- Main Content -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card border-0 custom-shadow shadow-lg">
          <div class="p-4">
            <!-- Search Filters -->
            <div class="section-container mb-4">
              <h3 class="text-custom-dark mb-3">Search Filters</h3>
              <div class="row g-3">
                <div class="col-md-6">
                  <input type="text" class="form-control bg-light" [(ngModel)]="reportId" 
                         placeholder="Enter Report ID">
                </div>
                <div class="col-md-4">
                  <input type="date" class="form-control bg-light" [(ngModel)]="filterDate">
                </div>
                <div class="col-md-2">
                  <div class="d-grid gap-2">
                    <button class="btn btn-success" (click)="filter()">Search</button>
                    <button class="btn btn-outline-secondary" (click)="clear()">Clear</button>
                  </div>
                </div>
              </div>
            </div>
  
            <!-- Reports List -->
            <div class="section-container">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Report ID</th>
                      <th>Date</th>
                      <th>Time</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let report of reports$ | async">
                      <td>{{ report.reportId }}</td>
                      <td>{{ report.date }}</td>
                      <td>{{ report.reportDate }}</td>
                      <td>
                        <button class="btn btn-sm btn-outline-primary me-2" (click)="openModal(report)">View</button>
                        <button class="btn btn-sm btn-outline-danger" (click)="deleteReport(report.reportId)">Delete</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Report Details Modal -->
  <div class="modal-overlay" *ngIf="isModalOpen">
    <div class="modal-container">
      <div class="alert-box">
        <div class="alert-content">
          <h2>Report Details</h2>
          <div class="alert-details">
            <div class="detail-row">
              <label><strong>Report ID:</strong></label>
              <span>{{selectedReport?.reportId}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Date:</strong></label>
              <span>{{selectedReport?.reportDate}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Note:</strong></label>
              <span>{{selectedReport?.note}}</span>
            </div>
          </div>
        </div>
        <div class="alert-button">
          <button class="btn btn-success me-2" (click)="downloadReport(selectedReport?.reportLink)">Download</button>
          <button class="btn btn-secondary" (click)="closeModal()">Close</button>
        </div>
      </div>
    </div>
  </div>