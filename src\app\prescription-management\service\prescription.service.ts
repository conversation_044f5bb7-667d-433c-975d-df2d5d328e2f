import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { Prescription } from '../model/prescription.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PrescriptionService {

  private API_URL = `${environment.PRESCRIPTION_MANAGEMENT_BASE_URL}`;

  constructor(private http: HttpClient) { }

  addPrescription(prescription: Prescription) : Observable<Prescription> {
    
    return this.http.post<Prescription>(this.API_URL, prescription);
  }
}
