.container {
  /* margin: 0; 
  padding: 0; */
  background-color: #E6FFE8;
  overflow: auto;
  /* width: 100vw;  */
  height: 100vh; 
  /* display: flex;  */
  /* flex-direction: column;  */
  /* position: relative; */
}

.content {
  /* flex-basis: 80%; */
  padding: 20px;
  background-color: #80CD84;
}

h1 {
  font-size: 2em;
  margin-bottom: 20px;
}

h3 {
  font-size: 1.5em;
  margin-bottom: 10px;
}


.header {
  display: flex;
  justify-content: flex-start; /* Align items to the start */
  align-items: center; /* Center vertically */
  gap: 10px; /* Reduce the space between items */
  margin-bottom: 20px;
}
/* .search-type {
  
  
  border-radius: 30px;
  background-color:  white;
  color: black;
} */

.search-results {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item button {
  padding: 5px 10px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
}

.result-item button:hover {
  background-color: #4CAF50;
}

.search-type,
.search-name {
  padding: 8px; /* Adjust padding for consistency */
  font-size: 1em;
  border-radius: 20px;
}

.search-name {
  width: 50%; /* Adjust width for balance */
}

.search-btn {
  padding: 8px 15px; /* Reduce padding for a compact button */
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 15px;
  cursor: pointer;
}

.search-btn:hover {
  background-color: #45a049;
}

.save-btn {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  border-radius: 20px;
}

.save-btn:hover {
  background-color: #45a049;
}


.personal-info, .medical-info {
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group input, .form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
  border-radius: 30px;
}


.gender {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gender label {
  margin-right: 20px;
}


.form-group .option {
  display: flex;
  flex-direction: column;
}


.option select {
  width: 100px;
  padding: 10px;
  margin-right: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;

}


@media screen and (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .sidebar-3 {
    flex-basis: 100%;
    border-right: none;
    margin-bottom: 20px;
  }

  .content {
    flex-basis: 100%;
  }

  .header {
    flex-direction: column;
  }
 
 
}