import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { Report } from '../../service/reportsAndRecords/report-record-env.service';
import { RecordService } from '../../service/reportsAndRecords/record.service';
import { Router, RouterLink } from '@angular/router';

@Component({
  selector: 'app-view-records',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './view-records.component.html',
  styleUrl: './view-records.component.css'
})
export class ViewRecordsComponent implements OnInit {
  records: any[] = [];
  isModalOpen = false;
  selectedRecord: any = null;
  reportList!: Report[];

  constructor(
    public http: HttpClient,
    private recordService: RecordService,
    private router: Router
  ) { }

  searchText: any = '';
  searchDate: any = '';

  ngOnInit() {
    this.fetchRecords();
  }



  fetchRecords() {
    this.http.get<any[]>('http://localhost:8083/record/all').subscribe({
      next: (data) => {
        this.records = data;
      },
      error: (error) => {
        console.error('Error fetching records:', error);
        alert('Failed to fetch records. Please try again later.');
      },
    });
  }

  openModal(record: any) {
    this.selectedRecord = record;
    this.isModalOpen = true;
  }

  closeModal() {
    this.isModalOpen = false;
    this.selectedRecord = null;
  }

  deleteRecord(record: any) {
    this.selectedRecord = record;
    if (!this.selectedRecord || !this.selectedRecord.recordId) {
      alert('No record selected for deletion.');
      return;
    }
    const isConfirmed = confirm(
      `Are you sure you want to delete the this record ?`
    );

    if (!isConfirmed) {
      return;
    }
    const recordId = this.selectedRecord.recordId;

    this.http.delete(`http://localhost:8083/record/${recordId}`).subscribe({
      next: () => {
        alert('Record deleted successfully.');
        this.fetchRecords();
        this.closeModal();
      },
      error: (error) => {
        console.error('Error deleting record:', error);
        alert('Failed to delete the record. Please try again later.');
      },
    });
    this.records = [];
    this.fetchRecords();
  }

  async updateRecord(record: any) {
    this.recordService.setCurrentRecord(record);
    this.router.navigate(['add-records']);
  }

  searchRecord() {
    const params: any = {};
    if (this.searchText) {
      params.recordId = this.searchText;
    }
    if (this.searchDate) {
      params.date = this.searchDate;
    }

    this.http.get('http://localhost:8083/record/search', { params }).subscribe(
      (response: any) => {
        this.records = response;
        if (!this.records || this.records.length === 0) {
          alert("No records found");
        }
        console.log(response);
      },
      (error) => {
        console.error(error);
      }
    );
  }

  clear() {
    this.searchText = '';
    this.searchDate = '';
    this.fetchRecords();
  }




}
