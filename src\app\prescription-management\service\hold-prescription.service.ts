import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { HoldPrescription } from '../model/hold-prescription.model';

@Injectable({
  providedIn: 'root'
})

export class HoldPrescriptionService {

  private API_URL = `${environment.PRESCRIPTION_MANAGEMENT_BASE_URL}/hold`;

  constructor(private http: HttpClient) {}

  holdPatientData(prescriptionData: HoldPrescription): Observable<string> {
    return this.http.post<string>(this.API_URL, prescriptionData, { responseType: 'text' as 'json' });
  }

  unholdPatientData(id: number): Observable<string> {
    return this.http.delete<string>(`${this.API_URL}/${id}`, { responseType: 'text' as 'json' });
  }

  getHoldPatientData(): Observable<HoldPrescription[]> {
    return this.http.get<HoldPrescription[]>(this.API_URL);
  }
}
