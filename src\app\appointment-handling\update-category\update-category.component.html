<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Update Category</p>
    </div>
    <div class="col-6"></div>
      <div class="col-2">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ Update Category
        </p>
      </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <form class="p-4" (ngSubmit)="updateCategory()">
          <!-- Search Section -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Search Category</h3>
            <div class="row g-3">
              <div class="col-md-6">
                <div class="input-group">
                  <input type="text" class="form-control bg-light" 
                         [(ngModel)]="searchTerm" (keyup.enter)="searchCategory()" name="searchTerm" 
                         placeholder="Enter Description" 
                         required />
                </div>
              </div>
            </div>
          </div>

          <!-- Category Information (Display after Search) -->
          <div *ngIf="searchedCategory && searchedCategory.categoryId" class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Category Information</h3>
            <div class="row g-3">
              <div class="col-md-6">
                <label for="categoryId">Category ID</label>
                <input type="text" class="form-control bg-light" 
                       id="categoryId" 
                       [value]="searchedCategory.categoryId" 
                       readonly name="categoryId" />
              </div>
              <div class="col-md-6">
                <label for="description">Description</label>
                <textarea class="form-control bg-light" 
                          id="description"
                          [(ngModel)]="searchedCategory.description" 
                          name="description" rows="3" required></textarea>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="d-flex justify-content-end mt-4">
            <button type="submit" class="btn btn-success px-4" [disabled]="!searchedCategory.categoryId">Update Category</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
