<div class="container-fluid mt-2">
    <div class="row col-12">
      <div class="col-4" id="prescription-name">
        <p>Doctors</p>
      </div>
  
      <div class="col-5" id="Search">
        <div class="search-bar">
          <input
            #searchInput
            type="text"
            class="form-control search-input"
            aria-label="Search"
          />
  
          <button class="search-btn">
            <i class="bi bi-search search-icon"></i>
          </button>
        </div>
      </div>
  
      <div class="col-1">
        <button class="slider">
          <i
            class="bi bi-sliders"
            data-bs-toggle="modal"
            data-bs-target="#exampleModal"
            data-bs-whatever="@mdo"
          ></i>
        </button>
      </div>
      <div class="col col-2" id="home-prescription-name">
        <p class="breadcrumb">
          <a routerLink="/" class="text-decoration-none">Home</a>/ Doctors
        </p>
      </div>
    </div>
  </div>

  <div class="container mt-4">
    <div class="row g-4">
      <div class="col-md-3" *ngFor="let doctor of doctors">
        <div class="vet-card">
          <div class="vet-avatar">
            <img
              [src]="doctor.profileImage"
              alt="Vet profile picture"
              class="profile-image"
            />
          </div>
          <div class="vet-info">
            <h3>{{ doctor.name }}</h3>
            <p class="reg-no">Reg No: {{ doctor.regNo }}</p>
            <p class="contact">{{ doctor.contact }}</p>
            <p class="email">{{ doctor.email }}</p>
          </div>
          <div class="vet-actions">
            <button class="btn btn-success">
              <i class="bi bi-pencil-square"></i>
            </button>
            <button class="btn btn-danger">
              <i class="bi bi-trash-fill"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  

  
