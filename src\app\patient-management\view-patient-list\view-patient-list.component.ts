import { Component } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { environment } from '../../../environments/environment';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-view-patient-list',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './view-patient-list.component.html',
  styleUrls: ['./view-patient-list.component.css']
})

export class ViewPatientListComponent {
    public patientList: any = [];
    private API_URL = environment.PATIENT_MANAGEMENT_BASE_URL

  constructor(private http:HttpClient) {
    this.loadTable();
  }

  loadTable(){
    this.http.get(`${this.API_URL}/patient-get-all`).subscribe(data=>{
      console.log(data);
      this.patientList=data;
    })
  }
}
