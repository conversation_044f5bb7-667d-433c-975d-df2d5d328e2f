import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, OnInit } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from './../../../environments/environment.development';
import { Patient } from './../model/patient.model';
import { Reports } from '../model/report.model';
import { format } from 'date-fns';
import { Appointment } from '../model/appointment.model';
import { Alert, AlertService } from './alert.service';
import { HoldPrescription } from '../model/hold-prescription.model';
import { Medication } from '../model/medication';

@Injectable({
  providedIn: 'root',
})
export class SearchService implements OnInit {
  alerts: Alert[] = [];
  private patientData = new BehaviorSubject<Patient | null>(null);
  patientData$ = this.patientData.asObservable();

  private reportData = new BehaviorSubject<Reports[] | null>(null);
  reportData$ = this.reportData.asObservable();

  private appointmentData = new BehaviorSubject<Appointment[] | null>(null);
  appointmentData$ = this.appointmentData.asObservable();

  private holdPatientData = new BehaviorSubject<Medication[] | null>(null);
  holdPatientData$ = this.holdPatientData.asObservable();

  constructor(private http: HttpClient, private alertService: AlertService) {}

  ngOnInit(): void {
    this.alertService.alert$.subscribe((alerts) => {
      this.alerts = alerts;
    });
  }

  searchAppointment(id: number): Observable<any> {
    return this.http.get<any>(
      `${environment.APPOINTMENT_MANAGEMENT_BASE_URL}/search-by-patientId/${id}`
    );
  }

  searchPatient(id: number): Observable<Patient> {
    return this.http.get<Patient>(
      `${environment.PATIENT_MANAGEMENT_BASE_URL}/patient-search-by-id/${id}`
    );
  }

  searchReport(id: string): Observable<Reports[]> {
    return this.http.get<Reports[]>(
      `${environment.REPORT_MANAGEMENT_BASE_URL}/patient/${id}`
    );
  }

  searchReportByDate(params: HttpParams): Observable<Reports[]> {
    return this.http.get<Reports[]>(
      `${environment.REPORT_MANAGEMENT_BASE_URL}/searchByDateAndPatientId`,
      { params }
    );
  }

  searchHoldPatient(id: number): Observable<Medication[]> {
    return this.http.get<Medication[]>(
      `${environment.PRESCRIPTION_MANAGEMENT_BASE_URL}/hold/${id}`
    );
  }

  getPatientData(id: number): void {
    this.searchAppointment(id).subscribe({
      next: (data) => {
        this.appointmentData.next(data);

        if (data && data.length > 0) {
          //Search Patient By Id
          this.searchPatient(id).subscribe({
            next: (patientData) => {
              this.patientData.next(patientData);
              localStorage.setItem(
                'currentPatient',
                JSON.stringify(patientData)
              );
            },
            error: (error) => {
              this.alertService.showAlert('error', error);
              this.patientData.next(null);
            },
          });

          //Get Patient reports
          this.getReports(id);
        } else {
          this.alertService.showAlert('error', 'No appointments found.');
        }
      },
      error: (error) => {
        console.error('Error fetching patient:', error);
      },
    });
  }

  getReports(id: number): void {
    const formattedId = this.formatIdToString(id);

    this.searchReport(formattedId).subscribe({
      next: (data) => this.reportData.next(data),
      error: (error) => {
        this.alertService.showAlert('error', 'No reports found.');
        this.reportData.next(null);
      },
    });
  }

  getHoldPatientData(holdPatient: HoldPrescription): void {
    this.getPatientData(holdPatient.patientId);

    this.searchHoldPatient(holdPatient.id!).subscribe({
      next: (data) => {
        this.holdPatientData.next(data);
      },
      error: (error) => {
        this.alertService.showAlert('error', error);
        this.holdPatientData.next(null);
      },
    });
  }

  getReportsByDate(startDate: Date, endDate: Date, patientID: number): void {
    const formattedId = this.formatIdToString(patientID);

    const formattedStartDate = format(startDate, 'yyyy-MM-dd');
    const formattedEndDate = format(endDate, 'yyyy-MM-dd');

    let params = new HttpParams();
    params = params.append('startDate', formattedStartDate);
    params = params.append('endDate', formattedEndDate);
    params = params.append('patientId', formattedId);

    this.searchReportByDate(params).subscribe({
      next: (data) => this.reportData.next(data),
      error: (error) => {
        this.alertService.showAlert('error', error);
        this.reportData.next(null);
      },
    });
  }

  formatIdToString(id: number): string {
    return `P${id.toString().padStart(4, '0')}`;
  }
}
