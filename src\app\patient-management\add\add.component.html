<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Add Patient</p>
    </div>
    <div class="col-6"></div>
    <div class="col-2">
      <p class="breadcrumb">
        <a routerLink="/" class="text-decoration-none">Home</a>/ Add Patient
      </p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <form #patientForm="ngForm" (ngSubmit)="onSubmit()" class="p-4">
          <!-- Personal Information -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Personal Information</h3>
            <div class="row g-3">
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light" 
                       [ngClass]="{'is-invalid': hasError('firstName')}"
                       [(ngModel)]="patient.firstName" 
                       name="firstName" 
                       placeholder="First Name">
                <div class="invalid-feedback" *ngIf="hasError('firstName')">
                  {{getError('firstName')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('lastName')}"
                       [(ngModel)]="patient.lastName" 
                       name="lastName" 
                       placeholder="Last Name">
                <div class="invalid-feedback" *ngIf="hasError('lastName')">
                  {{getError('lastName')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="number" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('age')}"
                       [(ngModel)]="patient.age" 
                       name="age" 
                       placeholder="Age">
                <div class="invalid-feedback" *ngIf="hasError('age')">
                  {{getError('age')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="date" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('dob')}"
                       [(ngModel)]="patient.dob" 
                       name="dob">
                <div class="invalid-feedback" *ngIf="hasError('dob')">
                  {{getError('dob')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('nic')}"
                       [(ngModel)]="patient.nic" 
                       name="nic" 
                       placeholder="NIC">
                <div class="invalid-feedback" *ngIf="hasError('nic')">
                  {{getError('nic')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="tel" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('contactNo')}"
                       [(ngModel)]="patient.contactNo" 
                       name="contactNo" 
                       placeholder="Contact Number">
                <div class="invalid-feedback" *ngIf="hasError('contactNo')">
                  {{getError('contactNo')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="email" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('email')}"
                       [(ngModel)]="patient.email" 
                       name="email" 
                       placeholder="Email">
                <div class="invalid-feedback" *ngIf="hasError('email')">
                  {{getError('email')}}
                </div>
              </div>
              <div class="col-md-4">
                <select class="form-control bg-light"
                        [ngClass]="{'is-invalid': hasError('gender')}"
                        [(ngModel)]="patient.gender" 
                        name="gender">
                  <option value="">Select Gender</option>
                  <option *ngFor="let type of genderTypes" [value]="type">{{type}}</option>
                </select>
                <div class="invalid-feedback" *ngIf="hasError('gender')">
                  {{getError('gender')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('address')}"
                       [(ngModel)]="patient.address" 
                       name="address" 
                       placeholder="Address">
                <div class="invalid-feedback" *ngIf="hasError('address')">
                  {{getError('address')}}
                </div>
              </div>
            </div>
          </div>

          <!-- Guardian Information -->
          <div class="section-container mb-4">
            <div class="d-flex align-items-center mb-3">
              <h3 class="text-custom-dark mb-0">Guardian Information</h3>
              <div class="form-check ms-3">
                <input type="checkbox" class="form-check-input" [(ngModel)]="hasGuardian" name="hasGuardian" id="hasGuardian">
                <label class="form-check-label text-custom-green" for="hasGuardian">Add Guardian</label>
              </div>
            </div>
            <div class="row g-3" *ngIf="hasGuardian">
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('guardianNIC')}"
                       [(ngModel)]="patient.guardianNIC" 
                       name="guardianNIC" 
                       placeholder="Guardian NIC">
                <div class="invalid-feedback" *ngIf="hasError('guardianNIC')">
                  {{getError('guardianNIC')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('guardianName')}"
                       [(ngModel)]="patient.guardianName" 
                       name="guardianName" 
                       placeholder="Guardian Name">
                <div class="invalid-feedback" *ngIf="hasError('guardianName')">
                  {{getError('guardianName')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="tel" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('guardianContact')}"
                       [(ngModel)]="patient.guardianContact" 
                       name="guardianContact" 
                       placeholder="Guardian Contact">
                <div class="invalid-feedback" *ngIf="hasError('guardianContact')">
                  {{getError('guardianContact')}}
                </div>
              </div>
            </div>
          </div>

          <!-- Medical Information -->
          <div class="section-container">
            <h3 class="text-custom-dark mb-3">Medical Information</h3>
            <div class="row g-3">
              <div class="col-md-4">
                <select class="form-control bg-light"
                        [ngClass]="{'is-invalid': hasError('bloodGroup')}"
                        [(ngModel)]="patient.bloodGroup" 
                        name="bloodGroup">
                  <option value="">Select Blood Group</option>
                  <option *ngFor="let type of bloodTypes" [value]="type">{{type}}</option>
                </select>
                <div class="invalid-feedback" *ngIf="hasError('bloodGroup')">
                  {{getError('bloodGroup')}}
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-check">
                  <input type="checkbox" class="form-check-input" [(ngModel)]="patient.patientStatus" name="patientStatus" id="patientStatus">
                  <label class="form-check-label text-custom-green" for="patientStatus">Currently Admitted</label>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-check">
                  <input type="checkbox" class="form-check-input" [(ngModel)]="patient.allergyStatus" name="allergyStatus" id="allergyStatus">
                  <label class="form-check-label text-custom-green" for="allergyStatus">Has Allergies</label>
                </div>
              </div>
              <div class="col-12">
                <textarea class="form-control bg-light"
                          [ngClass]="{'is-invalid': hasError('remarks')}"
                          [(ngModel)]="patient.remarks" 
                          name="remarks" 
                          placeholder="Remarks (Allergies description, etc.)" 
                          rows="3"></textarea>
                <div class="invalid-feedback" *ngIf="hasError('remarks')">
                  {{getError('remarks')}}
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="d-flex justify-content-end mt-4">
            <button type="submit" class="btn btn-success px-4">Add Patient</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal -->
<div class="modal-overlay" *ngIf="showSuccessModal">
  <div class="modal-container">
    <div class="modal-content">
      <div class="alert-box">
        <div class="alert-icon">
          <span>✓</span>
        </div>
        <div class="alert-content">
          <h2>Patient added successfully !</h2>
          <div class="alert-details">
            <div class="detail-row">
              <label><strong>Name:</strong></label>
              <span>{{patient.firstName}} {{patient.lastName}}</span>
            </div>
            <div class="detail-row">
              <label><strong>NIC:</strong></label>
              <span>{{patient.nic}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Contact No:</strong></label>
              <span>{{patient.contactNo}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Blood Group:</strong></label>
              <span>{{patient.bloodGroup}}</span>
            </div>
            <div class="detail-row" *ngIf="hasGuardian">
              <label><strong>Guardian:</strong></label>
              <span>{{patient.guardianName}}</span>
            </div>
          </div>
        </div>
        <div class="alert-button">
          <button class="btn btn-success" (click)="closeSuccessModal()">OK</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Error Modal -->
<div class="modal-overlay" *ngIf="showErrorModal">
  <div class="modal-container">
    <div class="modal-content">
      <div class="alert-box">
        <div class="alert-icon error">
          <span>✕</span>
        </div>
        <div class="alert-content">
          <h2 class="text-danger">Error Adding Patient</h2>
          <div class="alert-details">
            <p class="text-danger">{{errorMessage}}</p>
          </div>
        </div>
        <div class="alert-button">
          <button class="btn btn-danger" (click)="closeErrorModal()">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>