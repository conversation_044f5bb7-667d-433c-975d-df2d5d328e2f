import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule } from '@angular/forms';

import { PatientManagementComponent } from './patient-management.component';
import { ViewPatientListComponent } from './view-patient-list/view-patient-list.component';
import { SearchComponent } from './search/search.component';
import { AddComponent } from './add/add.component';
import { UpdateComponent } from './update/update.component';

const routes: Routes = [
  { path: '', component: ViewPatientListComponent }
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(routes),
    PatientManagementComponent,
    ViewPatientListComponent,
    SearchComponent,
    AddComponent,
    UpdateComponent
  ]
})
export class PatientManagementModule { }
