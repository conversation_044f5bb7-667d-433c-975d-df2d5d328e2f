<div class="alerts-container">
  <app-alert *ngFor="let alert of alerts" [alert]="alert"></app-alert>
</div>

<br />
<div class="fulcard">
  <div class="card shadow-lg p-3">
    <div class="header-container">
      <h3 class="text-success">Previous Reports</h3>
      <i
        class="bi bi-funnel-fill cursor-pointer text-success position-absolute fs-6"
        style="top: 10px; right: 10px; transform: translateY(-50%) scale(1.5)"
        role="button"
      ></i>
    </div>
    <div class="scrollable-container">
      <div
        class="card shadow-sm p-2 mb-3 card-shadow"
        *ngFor="let report of reportList"
        (click)="downloadPDF(report)"
      >
        <div class="d-flex align-items-center">
          <div class="pdf-image fs-2 me-3"></div>
          <div class="align-items-center mt-2">
            <h5>{{ report && report.reportDate }}</h5>
            <h6 class="report-category">{{ report && report.categoryType }}</h6>
          </div>
        </div>
      </div>
    </div>
    <br />
    <div class="button-container">
      <div></div>
      <i
        class="bi bi-stop-circle-fill text-success"
        data-bs-toggle="modal"
        data-bs-target="#holdModal"
        (click)="getAllHoldPrescription()"
      ></i>
    </div>
  </div>
</div>

<!-- Hold Modal  -->
<div class="modal" id="holdModal" *ngIf="isModalOpen">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="width: 550px">
      <div class="modal-header">
        <h4 class="modal-title text-success">Hold Patients</h4>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>

      <div
        class="modal-body"
        style="overflow-y: auto; height: 400px; width: 550px"
      >
        <table class="table table-hover">
          <thead>
            <tr class="text-center table-success">
              <th scope="col" class="text-success">Patient ID</th>
              <th scope="col" class="text-success">Patient Name</th>
              <th scope="col" class="text-success">Appoinment ID</th>
              <th scope="col" class="text-success">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr class="text-center" *ngFor="let patient of holdPatient">
              <td>{{ patient.patientId }}</td>
              <td>{{ patient.patientName }}</td>
              <td>{{ patient.appointmentId }}</td>
              <td>
                <div class="d-flex-centered">
                  <button
                    type="button"
                    class="btn btn-success me-2"
                    data-bs-dismiss="modal"
                    (click)="resumePatient(patient)"
                  >
                    <i class="bi bi-arrow-clockwise"></i>
                  </button>
                  <button type="button" class="btn me-2" (click)="unHoldPatient(patient)">
                    <i class="bi bi-trash3-fill text-danger"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="model-footer">
        <br />
        <br />
      </div>
    </div>
  </div>
</div>
