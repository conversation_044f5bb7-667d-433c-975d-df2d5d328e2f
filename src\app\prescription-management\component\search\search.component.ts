import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SearchService } from '../../service/search.service';
import { AlertComponent } from '../alert/alert.component';
import { Alert, AlertService } from '../../service/alert.service';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-search',
  standalone: true,
  imports: [CommonModule, FormsModule, AlertComponent, RouterLink],
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.css'],
})
export class SearchComponent implements OnInit {
  patientID: number  = 0;
  alerts: Alert[] = [];
  Search: any;
  startDate: string = ''; 
  endDate: string = '';

  constructor(
    private searchService: SearchService,
    private alertService: AlertService
  ) {}

  ngOnInit(): void {
    this.alertService.alert$.subscribe((alerts) => {
      this.alerts = alerts;
    });
  }

  get searchPlaceholder(): string {
    return this.patientID === 0 ? 'Search Patient...' : '';
  }
  
  searchByID(value: string): void {
    this.patientID = parseInt(value);
    if (this.patientID > 0) {
      this.searchService.getPatientData(this.patientID);
      localStorage.setItem('isPatientSearched', 'true');
    } else {
      this.alertService.showAlert('error', 'Please enter a valid patient ID.');
    }
  }

  searchByDate(): void {
    if (this.patientID > 0 && this.startDate && this.endDate) {
      const startDateObj = new Date(this.startDate);
      const endDateObj = new Date(this.endDate);

      if (startDateObj > endDateObj) {
        this.alertService.showAlert('error', 'Start date cannot be greater than end date.');
        return;
      }
      this.searchService.getReportsByDate(startDateObj, endDateObj, this.patientID);
    } else {
      this.alertService.showAlert('error', 'Please select both start and end dates.');
    }
  }
}
