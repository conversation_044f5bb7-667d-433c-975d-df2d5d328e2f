<div class="container">
  <div class="content">
    <h1>Search Patient</h1>
    <div class="header">
     
      <select [(ngModel)]="searchType" class="search-type">
        <option value="name">Search by Name</option>
        <option value="nic">Search by NIC</option>
        <option value="id">Search by ID</option>
      </select>
      <input type="text" [(ngModel)]="searchTerm" placeholder="Search Patient" class="search-name" />
      <button class="search-btn" (click)="search()">Search</button>
      
   
      <div class="form-group">
        <div class="search-results" *ngIf="searchResults.length > 0">
          <h3>Search Results</h3>
          <div class="result-item" *ngFor="let result of searchResults">
            <span>{{result.firstName}} {{result.lastName}} (NIC: {{result.nic}}) </span>
            <button (click)="selectPatient(result)">Select</button>
          </div>
        </div>
      </div>
    </div>
    <div class="personal-info">
      <h2>Personal Info - {{ patient.firstName }} {{ patient.lastName }}</h2>
      
        <div class="form-group">
          <input type="text" placeholder="First name" [(ngModel)]="patient.firstName" disabled>
          <input type="text" placeholder="Last name" [(ngModel)]="patient.lastName" disabled>
        </div>
        <div class="form-group">
          <input type="text" placeholder="email" [(ngModel)]="patient.email" disabled>
           <input type="date" [(ngModel)]="patient.dob" disabled/>
        </div>
        <div class="form-group">
          <input type="text" placeholder="Contact" [(ngModel)]="patient.contactNo" disabled />
          <input type="text" placeholder="NIC" [(ngModel)]="patient.nic" disabled/>
        </div>
        <div class="form-group">
          <input type="text" placeholder="address" [(ngModel)]="patient.address" disabled />
        </div>
        <div class="form-group">
          <div class="gender">
            <label><h3>Gender:</h3></label>
            <label>
              <input type="radio" name="gender" [(ngModel)]="patient.gender" value="Male" disabled/> Male
            </label>
            <label>
              <input type="radio" name="gender" [(ngModel)]="patient.gender" value="Female" disabled/> Female
            </label>
            <label>
              <input type="radio" name="gender" [(ngModel)]="patient.gender" value="Other" disabled/> Other
            </label>
          </div>
          </div>
        </div> 
        <div class="Guardian-info">
          <h2>Guardian Info</h2>
        <div class="form-group">
          <input type="text" [(ngModel)]="patient.guardianName" disabled />
          <input type="text" [(ngModel)]="patient.guardianContact" disabled />
          <input type="text" [(ngModel)]="patient.guardianNIC" disabled  />
        </div> 
      </div>
   
   <div class="medical-info">
    <h2>Medical Info & History</h2>
    <div class="form-group">
      <label ><h3>Blood Group :</h3></label>
      <div class="option" >
        <select [(ngModel)]="patient.bloodGroup" disabled >
          <option value="O+">O+</option>
          <option value="O-">O-</option>
          <option value="A+">A+</option>
          <option value="B+">B+</option>
          <option value="AB+">AB+</option>
          <option value="AB-">AB-</option>
        </select>
      </div>
        
        <label>
          <input type="checkbox" [(ngModel)]="patient.patientStatus" name="patientStatus" disabled> Currently Admitted
        </label>


        <label>
          <input type="checkbox" [(ngModel)]="patient.allergyStatus" name="allergyStatus" disabled> Has Allergies
        </label>


          
        </div>
        <div class="form-group">
          <input type="text" [(ngModel)]="patient.remarks" placeholder="Remarks (Allergies description, etc.)" disabled/>
        </div>

      </div> 
  </div>
</div>
