<div class="container">
  <!-- Header Section -->
  <div class="row">
    <div class="col-4">
      <p class="page-title">Update Patient</p>
    </div>
    <div class="col-6"></div>
    <div class="col-2">
      <p class="breadcrumb">
        <a routerLink="/" class="text-decoration-none">Home</a>/ Update Patient
      </p>
    </div>
  </div>

  <!-- Search Section -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg p-4">
        <div class="row g-3">
          <div class="col-md-3">
            <select [(ngModel)]="searchType" class="form-control bg-light">
              <option value="name">Search by Name</option>
              <option value="nic">Search by NIC</option>
              <option value="id">Search by ID</option>
            </select>
          </div>
          <div class="col-md-7">
            <div class="search-bar">
              <input type="text" [(ngModel)]="searchTerm" class="form-control search-input" placeholder="Search Patient" (keyup.enter)="search()">
              <button class="search-btn" (click)="search()">
                <i class="bi bi-search search-icon"></i>
              </button>
            </div>
          </div>
          <div class="col-md-2">
            <button class="btn btn-success w-100" (click)="saveCustomer()">Save Changes</button>
          </div>
        </div>

        <!-- Search Results -->
        <div class="mt-4" *ngIf="searchResults.length > 0">
          <h3 class="text-custom-dark mb-3">Search Results</h3>
          <div class="section-container">
            <div class="result-item d-flex align-items-center justify-content-between p-3 mb-2" *ngFor="let result of searchResults">
              <span class="text-custom-green">{{result.firstName}} {{result.lastName}} (NIC: {{result.nic}})</span>
              <button class="btn btn-outline-success btn-sm" (click)="selectPatient(result)">Select</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Patient Information Form -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 custom-shadow shadow-lg">
        <form class="p-4">
          <!-- Personal Information -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Personal Information</h3>
            <div class="row g-3">
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light" 
                       [ngClass]="{'is-invalid': hasError('firstName')}"
                       [(ngModel)]="patient.firstName" 
                       name="firstName" 
                       placeholder="First Name">
                <div class="invalid-feedback" *ngIf="hasError('firstName')">
                  {{getError('firstName')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('lastName')}"
                       [(ngModel)]="patient.lastName" 
                       name="lastName" 
                       placeholder="Last Name">
                <div class="invalid-feedback" *ngIf="hasError('lastName')">
                  {{getError('lastName')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="number" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('age')}"
                       [(ngModel)]="patient.age" 
                       name="age" 
                       placeholder="Age">
                <div class="invalid-feedback" *ngIf="hasError('age')">
                  {{getError('age')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="date" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('dob')}"
                       [(ngModel)]="patient.dob" 
                       name="dob">
                <div class="invalid-feedback" *ngIf="hasError('dob')">
                  {{getError('dob')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('nic')}"
                       [(ngModel)]="patient.nic" 
                       name="nic" 
                       placeholder="NIC">
                <div class="invalid-feedback" *ngIf="hasError('nic')">
                  {{getError('nic')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="tel" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('contactNo')}"
                       [(ngModel)]="patient.contactNo" 
                       name="contactNo" 
                       placeholder="Contact Number">
                <div class="invalid-feedback" *ngIf="hasError('contactNo')">
                  {{getError('contactNo')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="email" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('email')}"
                       [(ngModel)]="patient.email" 
                       name="email" 
                       placeholder="Email">
                <div class="invalid-feedback" *ngIf="hasError('email')">
                  {{getError('email')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('address')}"
                       [(ngModel)]="patient.address" 
                       name="address" 
                       placeholder="Address">
                <div class="invalid-feedback" *ngIf="hasError('address')">
                  {{getError('address')}}
                </div>
              </div>
              <div class="col-md-4">
                <div class="d-flex gap-3">
                  <div class="form-check">
                    <input type="radio" 
                           class="form-check-input" 
                           [ngClass]="{'is-invalid': hasError('gender')}"
                           [(ngModel)]="patient.gender" 
                           name="gender" 
                           value="Male" 
                           id="male">
                    <label class="form-check-label text-custom-green" for="male">Male</label>
                  </div>
                  <div class="form-check">
                    <input type="radio" 
                           class="form-check-input"
                           [(ngModel)]="patient.gender" 
                           name="gender" 
                           value="Female" 
                           id="female">
                    <label class="form-check-label text-custom-green" for="female">Female</label>
                  </div>
                  <div class="form-check">
                    <input type="radio" 
                           class="form-check-input"
                           [(ngModel)]="patient.gender" 
                           name="gender" 
                           value="Other" 
                           id="other">
                    <label class="form-check-label text-custom-green" for="other">Other</label>
                  </div>
                </div>
                <div class="invalid-feedback" *ngIf="hasError('gender')">
                  {{getError('gender')}}
                </div>
              </div>
            </div>
          </div>

          <!-- Guardian Information -->
          <div class="section-container mb-4">
            <h3 class="text-custom-dark mb-3">Guardian Information</h3>
            <div class="row g-3">
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('guardianNIC')}"
                       [(ngModel)]="patient.guardianNIC" 
                       name="guardianNIC" 
                       placeholder="Guardian NIC">
                <div class="invalid-feedback" *ngIf="hasError('guardianNIC')">
                  {{getError('guardianNIC')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="text" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('guardianName')}"
                       [(ngModel)]="patient.guardianName" 
                       name="guardianName" 
                       placeholder="Guardian Name">
                <div class="invalid-feedback" *ngIf="hasError('guardianName')">
                  {{getError('guardianName')}}
                </div>
              </div>
              <div class="col-md-4">
                <input type="tel" 
                       class="form-control bg-light"
                       [ngClass]="{'is-invalid': hasError('guardianContact')}"
                       [(ngModel)]="patient.guardianContact" 
                       name="guardianContact" 
                       placeholder="Guardian Contact">
                <div class="invalid-feedback" *ngIf="hasError('guardianContact')">
                  {{getError('guardianContact')}}
                </div>
              </div>
            </div>
          </div>

          <!-- Medical Information -->
          <div class="section-container">
            <h3 class="text-custom-dark mb-3">Medical Information</h3>
            <div class="row g-3">
              <div class="col-md-4">
                <select class="form-control bg-light"
                        [ngClass]="{'is-invalid': hasError('bloodGroup')}"
                        [(ngModel)]="patient.bloodGroup" 
                        name="bloodGroup">
                  <option value="">Select Blood Group</option>
                  <option *ngFor="let type of bloodTypes" [value]="type">{{type}}</option>
                </select>
                <div class="invalid-feedback" *ngIf="hasError('bloodGroup')">
                  {{getError('bloodGroup')}}
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-check">
                  <input type="checkbox" 
                         class="form-check-input" 
                         [(ngModel)]="patient.patientStatus" 
                         name="patientStatus" 
                         id="patientStatus">
                  <label class="form-check-label text-custom-green" for="patientStatus">Currently Admitted</label>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-check">
                  <input type="checkbox" 
                         class="form-check-input" 
                         [(ngModel)]="patient.allergyStatus" 
                         name="allergyStatus" 
                         id="allergyStatus">
                  <label class="form-check-label text-custom-green" for="allergyStatus">Has Allergies</label>
                </div>
              </div>
              <div class="col-12">
                <textarea class="form-control bg-light" 
                          [(ngModel)]="patient.remarks" 
                          name="remarks" 
                          placeholder="Remarks (Allergies description, etc.)" 
                          rows="3"></textarea>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Success Modal -->
<div class="modal-overlay" *ngIf="showSuccessModal">
  <div class="modal-container">
    <div class="modal-content">
      <div class="alert-box">
        <div class="alert-icon">
          <span>✓</span>
        </div>
        <div class="alert-content">
          <h2>Patient successfully updated!</h2>
          <div class="alert-details">
            <div class="detail-row">
              <label><strong>Name:</strong></label>
              <span>{{patient.firstName}} {{patient.lastName}}</span>
            </div>
            <div class="detail-row">
              <label><strong>NIC:</strong></label>
              <span>{{patient.nic}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Contact No:</strong></label>
              <span>{{patient.contactNo}}</span>
            </div>
            <div class="detail-row">
              <label><strong>Blood Group:</strong></label>
              <span>{{patient.bloodGroup}}</span>
            </div>
          </div>
        </div>
        <div class="alert-button">
          <button class="btn btn-success" (click)="closeSuccessModal()">OK</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Error Modal -->
<div class="modal-overlay" *ngIf="showErrorModal">
  <div class="modal-container">
    <div class="modal-content">
      <div class="alert-box">
        <div class="alert-icon error">
          <span>✕</span>
        </div>
        <div class="alert-content">
          <h2 class="text-danger">Error Updating Patient</h2>
          <div class="alert-details">
            <p class="text-danger">{{errorMessage}}</p>
          </div>
        </div>
        <div class="alert-button">
          <button class="btn btn-danger" (click)="closeErrorModal()">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>