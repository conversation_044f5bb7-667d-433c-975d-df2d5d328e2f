

export interface Patient {
  id: number;
  name: string;
  doctor: string;
  checkUpType: string;
  date: string;
  time: string;
  status: 'Completed' | 'Cancelled';
}

export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  isAvailable: boolean;
  nextAvailable?: string;
}

export interface AppointmentStats {
  total: number;
  completed: number;
  cancelled: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
    tension?: number;
  }[];
}
