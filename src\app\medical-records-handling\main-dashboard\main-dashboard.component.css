body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #d4f1d1;
}

.header {
    background-color: #E6FFE8;
    padding: 20px;
    text-align: center;
    color: rgb(0, 0, 0);
}

.header h1 {
    margin: 0;
    font-size: 3.5em;
}

.container {
    display: flex;
    padding: 50px;
    align-items: center;
}

.image-container {
    width: 50%;
}

.image-container img {
    max-width: 100%;
}

.button-container {
    width: 30%;
    background-color: #ecf9ec;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.button-container a {
    text-decoration: none;
}

.button-container button {
    display: block;
    width: 100%;
    padding: 15px;
    margin: 20px 0;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1.2em;
    cursor: pointer;
}

.button-container button:hover {
    background-color: #45a049;
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
        align-items: center;
        padding: 20px;
    }

    .image-container {
        width: 100%;
        margin-bottom: 20px;
    }

    .button-container {
        width: 100%;
        padding: 20px;
    }

    .header h1 {
        font-size: 2em;
    }
}
