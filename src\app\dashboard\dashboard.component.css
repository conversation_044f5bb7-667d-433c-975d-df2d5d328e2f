.dashboard-container {
  min-height: 100vh;
  background: #DCF4DA;
  color: #2c3e50;
}
.header {
  background: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}
.header-marquee {
  width: 72rem;
  background-color: #E6FFE8;
  padding: 0.5rem 1rem;
  white-space: nowrap;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-marquee p {
  display: inline-block;
  padding-left: 100%;
  animation: marquee 15s linear infinite;
  font-size: 1rem;
  color: #2c3e50;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

.add-patient-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #4CAF50;
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-patient-btn:hover {
  background: #0EAF13;
  transform: translateY(-2px);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  min-width: 20px;
  text-align: center;
}
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.welcome-text h2 {
  font-size: 1.8rem;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.status-text {
  color: #4CAF50;
  font-size: 1rem;
}

.datetime-box {
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
}

.digital-clock {
  font-size: 1.8rem;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 0.25rem;
}

.calendar {
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}
.stat-card:hover {
  transform: translateY(-5px);
}
.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4CAF50;
}

.stat-content .number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.stat-change.positive {
  color: #4CAF50;
}

.stat-change.negative {
  color: #ef4444;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-card, .schedule-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-legend {
  display: flex;
  gap: 1.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.primary {
  background: #4CAF50;
}

.legend-color.secondary {
  background: #81C784;
}

/* Schedule Styles */
.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  background: #f8fafc;
  transition: transform 0.3s ease;
}

.schedule-item:hover {
  transform: translateX(5px);
  background: #E8F5E9;
}

.schedule-info p {
  color: #64748b;
  margin-top: 0.25rem;
}

.schedule-time {
  color: #64748b;
  font-size: 0.875rem;
}

/* Room Grid */
.room-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.room-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.room-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.room-numbers .number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.occupancy-rate {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4CAF50;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }

  .welcome-section {
    flex-direction: column;
    gap: 1rem;
  }

  .datetime-box {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 640px) {
  .main-content {
    padding: 1rem;
  }

  .logo-text {
    font-size: 1.5rem;
  }

  .add-patient-btn {
    padding: 0.5rem 1rem;
  }
}